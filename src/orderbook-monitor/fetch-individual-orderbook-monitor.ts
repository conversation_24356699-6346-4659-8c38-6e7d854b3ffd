import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { logger } from '../utils/pinno-logger';

export class FetchIndividualOrderbookMonitor extends BaseOrderbookMonitor {
  isRunning = false;
  private currentFetchIndex = -1;

  public override subscribe(symbol: string): boolean {
    if (this.subscribedSymbols.includes(symbol)) {
      return false;
    }

    // Insert after current item to fetch it next
    this.subscribedSymbols.splice(this.currentFetchIndex + 1, 0, symbol);

    if (!this.isMonitored(symbol)) {
      this.startMonitoring(symbol);
    }

    if (!this.isRunning) {
      this.currentFetchIndex = -1;
      this.isRunning = true;
      this.fetchLoop()
        .catch((error) => logger.error(error, 'FetchIndividualOrderbookMonitor fetchLoop error:'))
        .finally(() => {
          this.isRunning = false;
          if (this.onMonitoringFinish != undefined) {
            this.onMonitoringFinish();
          }
        });
    }
    return true;
  }

  public override unsubscribe(symbol: string): boolean {
    const indexToRemove = this.subscribedSymbols.indexOf(symbol);
    if (indexToRemove === -1) {
      return false;
    }

    this.subscribedSymbols.splice(indexToRemove, 1);

    // Adjust current index if necessary
    if (indexToRemove <= this.currentFetchIndex) {
      this.currentFetchIndex--;
    }

    // instantly stop monitoring if it's not fetching this symbols right now
    if (this.currentFetchSymbol !== symbol && this.isMonitored(symbol)) {
      this.stopMonitoring(symbol);
    }
    return true;
  }

  requestsInterval = 1000;
  private async fetchLoop(): Promise<void> {
    while (this.subscribedSymbols.length > 0) {
      // Move to next symbol for the next iteration
      this.currentFetchIndex = (this.currentFetchIndex + 1) % this.subscribedSymbols.length;
      const symbol = this.subscribedSymbols[this.currentFetchIndex];
      const beginRequestTime = Date.now();
      // fetch till request isn't in the list
      const wasUpdated = await this.fetchSymbol(symbol);
      // if symbol was unsubscribed during the fetch, now we can stop monitoring it
      if (!this.subscribedSymbols.includes(symbol) && this.isMonitored(symbol)) {
        this.stopMonitoring(symbol);
      }

      if (this.subscribedSymbols.length === 0) {
        break;
      }
      
      const requestDuration = Date.now() - beginRequestTime;
      const diff = this.requestsInterval - requestDuration;
      if (diff > 100) {
        // additional wait for diff ms to keep the request rate
        await new Promise(resolve => setTimeout(resolve, diff));
      }
    }
  }

  currentFetchSymbol: string = undefined;

  private async fetchSymbol(symbol: string): Promise<boolean> {
    this.currentFetchSymbol = symbol;
    let updateRecord = this.newSymbolUpdateRecord();
    try {
      const orderbook = await this.exchange.fetchOrderBook(symbol);
      this.commitSymbolUpdateRecord(symbol, orderbook, updateRecord);
      this.notifySymbolUpdate(symbol, orderbook);
      return true;
    } catch (error) {
      this.commitSymbolUpdateRecord(symbol, error, updateRecord);
      this.notifySymbolUpdate(symbol, error);
      logger.error(error, 'FetchIndividualExchangeMonitor fetchSymbol error:');
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    } finally {
      this.currentFetchSymbol = undefined;
    }
  }
}