import * as ccxt from 'ccxt';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { exchanges } from '../core/exchanges-provider';
import { LINQ } from '../utils/linq';
import { logger } from '../utils/pinno-logger';
import { MonitoredSymbolsRegistry } from '../utils/monitored-symbols-registry';
import { getStartWatchScheduler, getStopWatchScheduler } from '../utils/exchange-task-sheduler';
import { ExchangeHelper } from '../utils/exchange-helper';
import { getConnectionsHelper } from '../utils/exchange-connections-helpers';

export class WatchIndividualExchangeMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;
  id: number = WatchIndividualExchangeMonitor.idCounter++;

  override async initialize(): Promise<void> {
    await super.initialize();
    if (this.configs.individualMonitors_useSeparateExchange) {
      // coinbase only allows 30 symbols per connection and only 1 connection.
      // so we need to create a new exchange instance for each group.
      this.exchange = await exchanges.createExchange(this.exchangeName);
    }
  }

  override async destroy(): Promise<void> {
    if (this.configs.individualMonitors_useSeparateExchange) {
      logger.info(`Closing exchange for WatchIndividualExchangeMonitor (${this.id})`);
      await this.exchange.close();
    }
    await super.destroy();
  }

  public override subscribe(symbol: string): boolean {
    const result = super.subscribe(symbol);
    if (!result) {
      return false;
    }

    this.startMonitoring(symbol);
    this.loop(symbol)
      .catch((error) => logger.error(error, `WatchIndividualExchangeMonitor ${this.id} symbolLoop error:`))
      .finally(() => {
        this.stopMonitoring(symbol);
        if (this.monitoringRecords.size === 0 && this.onMonitoringFinish != undefined) {
          this.onMonitoringFinish();
        }
      });

    return result;
  }

  private loop: (symbol: string) => Promise<void> = this.getLoopMethod().bind(this);

  private getLoopMethod(): (symbol: string) => Promise<void> {
    const methodName = this.configs.individualMonitors_LoopMethod;
    logger.info(`[${this.exchangeName}] WatchIndividualExchangeMonitor '${this.id}' use '${methodName}'`);
    return this[methodName] as (symbol: string) => Promise<void>;
  }

  private async normalUnwatchLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);

    while (this.subscribedSymbols.includes(symbol)) {
      let orderbook: ccxt.OrderBook | Error = new Error('undefined orderbook');
      while (this.subscribedSymbols.includes(symbol)) {
        if (orderbook instanceof Error) {
          // delay before the next attempt
          await getStartWatchScheduler(this.exchange).schedule(symbol);
        }
        orderbook = await this.watchSymbol(symbol);
      }

      await getStopWatchScheduler(this.exchange).schedule(symbol);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    await this.unwatchSymbol(symbol);
  }

  private async closeConnectionLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    let beforeEstablishedConnectionClients: WsClient[] = undefined;

    // first update
    let orderbook: ccxt.OrderBook | Error = new Error('undefined orderbook');
    while (orderbook instanceof Error) {
      if (orderbook instanceof Error) {
        // delay before the next attempt
        await getStartWatchScheduler(this.exchange).schedule(symbol);
      }
      if (beforeEstablishedConnectionClients === undefined) {
        beforeEstablishedConnectionClients = LINQ.toArray(Object.values(this.exchange.clients));
      }
      orderbook = await this.watchSymbol(symbol);
    }

    const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
    let client = connectionHelper.findClientServingSymbols(symbols, beforeEstablishedConnectionClients);
    if (client) {
      connectionHelper.stopClosingConnection(client, symbols);
    } else {
      throw new Error(`WatchIndividualExchangeMonitor '${this.id}' failed to find a client responsible for '${symbol}'`);
    }

    while (this.subscribedSymbols.includes(symbol)) {
      while (this.subscribedSymbols.includes(symbol)) {
        if (orderbook instanceof Error) {
          // delay before the next attempt
          await getStartWatchScheduler(this.exchange).schedule(symbol);
        }
        orderbook = await this.watchSymbol(symbol);
      }

      await getStopWatchScheduler(this.exchange).schedule(symbol);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    await this.closeConnectionForSymbol(client, symbol);
  }

  private async forceUnwatchLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);

    while (this.subscribedSymbols.includes(symbol)) {
      let orderbook: ccxt.OrderBook | Error = new Error('undefined orderbook');
      while (this.subscribedSymbols.includes(symbol)) {
        if (orderbook instanceof Error) {
          // delay before the next attempt
          await getStartWatchScheduler(this.exchange).schedule(symbol);
        }
        orderbook = await this.watchSymbol(symbol);
      }

      await getStopWatchScheduler(this.exchange).schedule(symbol);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
    if (!MonitoredSymbolsRegistry.isMonitored(this.exchange, symbol)) {
      await this.unwatchSymbol(symbol);
      getConnectionsHelper(this.exchange).cleanupUnsubscribeRejections(symbol);
    }
  }

  private async closeExchangeLoop(symbol: string): Promise<void> {
    const symbols = [symbol];
    MonitoredSymbolsRegistry.register(this.exchange, symbols);

    while (this.subscribedSymbols.includes(symbol)) {
      let orderbook: ccxt.OrderBook | Error = new Error('undefined orderbook');
      while (this.subscribedSymbols.includes(symbol)) {
        if (orderbook instanceof Error) {
          // delay before the next attempt
          await getStartWatchScheduler(this.exchange).schedule(symbol);
        }
        orderbook = await this.watchSymbol(symbol);
      }

      await getStopWatchScheduler(this.exchange).schedule(symbol);
    }

    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
  }

  private async watchSymbol(symbol: string): Promise<ccxt.OrderBook | Error> {
    let updateRecord = this.newSymbolUpdateRecord();
    try {
      const orderbook = await this.exchange.watchOrderBook(symbol);
      this.commitSymbolUpdateRecord(symbol, orderbook, updateRecord);
      this.notifySymbolUpdate(symbol, orderbook);
      return orderbook;
    } catch (error) {
      // if (error instanceof ExchangeClosedByUser) {
      //   // the underlying connection was closed, we should not watch these symbols anymore
      //   logger.warn(`WatchIndividualExchangeMonitor (${symbol}) connection closed, but we're still watching it`);
      //   return error;
      // }

      if (error instanceof ccxt.ChecksumError) {
        // ChecksumError is a typical error, no need to log it. 
        // Usually it signalizes that server has high trading activity at the moment, 
        // and our app cannot keep up with updates during subscription phase, 
        // or node server is overloaded and cannot process updates fast enough.
        // Either way, our retry logic will handle it.
        logger.warn(`WatchIndividualExchangeMonitor '${this.id}' 'watchSymbol' '${symbol}' ChecksumError.`);
      } else {
        logger.error(error, `WatchIndividualExchangeMonitor '${this.id}' 'watchSymbol' '${symbol}' error:`);
      }

      this.commitSymbolUpdateRecord(symbol, error, updateRecord);
      this.notifySymbolUpdate(symbol, error);
      return error;
    }
  }

  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      const result = await this.exchange.unWatchOrderBook(symbol);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchIndividualExchangeMonitor ${this.id} Failed to unwatch '${symbol}'`);
        return false;
      }

      logger.info(`WatchIndividualExchangeMonitor ${this.id} Unwatched '${symbol}'`);
      return true;
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor ${this.id} 'unwatchSymbol' '${symbol}' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async closeConnectionForSymbol(client: WsClient, symbol: string): Promise<boolean> {
    try {
      const symbols = [symbol];
      return await ExchangeHelper.getConnectionsHelper(this.exchange).tryToCloseConnection(client, symbols);
    } catch (error) {
      logger.error(error, `WatchIndividualExchangeMonitor 'closeConnectionForSymbol' ${symbol} error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}
