import * as ccxt from 'ccxt';

export class SymbolMonitoringRecord {
  symbol: string;
  startTime: Date | undefined = undefined;
  updates: SymbolUpdateRecord[] = [];
  endTime: Date | undefined = undefined;

  public getFirstUpdate(): SymbolUpdateRecord | undefined {
    if (this.updates.length === 0) {
      return undefined;
    }

    return this.updates[0];
  }

  public getFirstSuccessfulUpdate(): SymbolUpdateRecord | undefined {
    if (this.updates.length === 0) {
      return undefined;
    }

    for (let i = 0; i < this.updates.length; i++) {
      if (this.updates[i].value instanceof Error) {
        continue;
      }

      return this.updates[i];
    }

    return undefined;
  }
}

export class SymbolUpdateRecord {
  requestTime: Date | undefined = undefined;
  value: ccxt.OrderBook | Error = undefined;
  responseTime: Date | undefined = undefined;
}
