import { CancelToken } from '@esfx/canceltoken';
import { BaseOrderbookMonitor } from '../base-orderbook-monitor';
import { logger } from '../../utils/pinno-logger';

export class RebalanceTransition {
  symbol: string;
  from: BaseOrderbookMonitor;
  to: BaseOrderbookMonitor;
  upgradePromise: Promise<boolean>;
  startTime = new Date();
  state: 'inProgress' | 'success' | 'failed' = 'inProgress';
  endTime: Date | undefined = undefined;
  private cancelSource: { cancel(): void; token: CancelToken } | undefined;

  constructor(symbol: string, from: BaseOrderbookMonitor, to: BaseOrderbookMonitor) {
    this.symbol = symbol;
    this.from = from;
    this.to = to;

    this.cancelSource = CancelToken.source();
    
    if (!this.to.isSubscribed(symbol)) {
      this.to.subscribe(symbol);
    }

    //logger.info(`'${symbol}' Rebalance '${from.constructor.name}' -> '${to.constructor.name}' start`);
    this.upgradePromise = to.waitNextSuccessfulUpdate(symbol, this.cancelSource.token)
      .then((result) => {
        if (result) {
          this.onSuccess();
        } else {
          this.onFailed();
        }
        return result;
      }).catch(error => {
        logger.error(error, `'${symbol}' Rebalance '${from.constructor.name}' -> '${to.constructor.name}' error:`);
        throw error;
      });
  }

  public cancel() {
    if (this.cancelSource) {
      this.cancelSource.cancel();
      this.cancelSource = undefined;
      logger.info(`'${this.symbol}' Rebalance '${this.from.constructor.name}' -> '${this.to.constructor.name}' cancel`);
    }
  }

  private onSuccess() {
    this.state = 'success';
    this.endTime = new Date();
    //logger.info(`'${this.symbol}' Rebalance '${this.from.constructor.name}' -> '${this.to.constructor.name}' success`);

    if (this.from.isSubscribed(this.symbol)) {
      this.from.unsubscribe(this.symbol);
    }
  }

  private onFailed() {
    this.state = 'failed';
    this.endTime = new Date();

    //logger.info(`'${this.symbol}' Rebalance '${this.from.constructor.name}' -> '${this.to.constructor.name}' failed`);
    if (this.to.isSubscribed(this.symbol)) {
      this.to.unsubscribe(this.symbol);
    }
  }
}