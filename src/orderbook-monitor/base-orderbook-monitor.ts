import * as ccxt from 'ccxt';
import { CancelToken } from '@esfx/canceltoken';
import { ExchangeMonitoringConfig, exchangeMonitoringConfigs } from '../core/exchange-monitoring-configs';
import { SymbolMonitoringRecord, SymbolUpdateRecord } from './helpers/symbol-monitoring-record';
import { exchanges } from '../core/exchanges-provider';
import { orderbookCache } from '../core/orderbook-cache';
import { DataUpdatedHandler, updateNotifier } from '../core/orderbook-update-notifier';
import { Deferred } from '@esfx/async-deferred';
import { logger } from '../utils/pinno-logger';

type MonitoringStartedHandler = (monitoringRecord: SymbolMonitoringRecord) => void;
type MonitoringUpdatedHandler = (symbol: string, updateRecord: SymbolUpdateRecord) => void;
type MonitoringStoppedHandler = (monitoringRecord: SymbolMonitoringRecord) => void;

export class BaseOrderbookMonitor {
  exchangeName: string;
  protected exchange: ccxt.Exchange;
  protected configs: ExchangeMonitoringConfig;
  protected subscribedSymbols: string[] = [];

  public monitoringRecords = new Map<string, SymbolMonitoringRecord>();

  // events
  monitoringStarted:MonitoringStartedHandler[] = [];
  monitoringUpdated:MonitoringUpdatedHandler[] = []
  monitoringStopped:MonitoringStoppedHandler[] = [];

  protected finishedMonitoringRecords = new Map<string, SymbolMonitoringRecord[]>();
  
  onMonitoringFinish: () => void;

  constructor(exchangeName: string) {
    this.exchangeName = exchangeName;
    this.exchange = exchanges.get(exchangeName);
    this.configs = exchangeMonitoringConfigs.get(exchangeName);
  }

  public async initialize(): Promise<void> {
    return;
  }

  public subscribe(symbol: string): boolean {
    if (!this.subscribedSymbols.includes(symbol)) {
      this.subscribedSymbols.push(symbol);
      return true;
    }

    return false;
  }

  public isSubscribed(symbol: string): boolean {
    return this.subscribedSymbols.includes(symbol);
  }

  public getSubscribedSymbols(): IterableIterator<string> {
    return this.subscribedSymbols.values();
  }

  public unsubscribe(symbol: string): boolean {
    const index = this.subscribedSymbols.indexOf(symbol);
    if (index !== -1) {
      this.subscribedSymbols.splice(index, 1);
      return true;
    }

    return false;
  }

  protected startMonitoring(symbol: string) {
    if (!this.monitoringRecords.has(symbol)) {
      const monitoringRecord = new SymbolMonitoringRecord();
      monitoringRecord.symbol = symbol;
      monitoringRecord.startTime = new Date();
      this.monitoringRecords.set(symbol, monitoringRecord);
      
      for (const handler of this.monitoringStarted) {
        handler(monitoringRecord);
      }
    }
    return true;
  }

  // CAUTION! when monitoring finishing is still in the progress (you might still receive the final updates), 
  // 'isMonitored' can return 'true' even if 'isSubscribed' returns 'false' for the symbol.
  public isMonitored(symbol: string): boolean {
    return this.monitoringRecords.has(symbol);
  }
  
  public hasReceivedFirstSuccessfulUpdate(symbol: string): boolean {
    const monitoringRecord = this.monitoringRecords.get(symbol);
    if (!monitoringRecord) {
      return false;
    }
    return monitoringRecord.getFirstSuccessfulUpdate() !== undefined;
  }

  public getMonitoredSymbols(): IterableIterator<string> {
    return this.monitoringRecords.keys();
  }

  protected stopMonitoring(symbol: string) {
    const monitoringRecord = this.monitoringRecords.get(symbol);
    if (!monitoringRecord) {
      return;
    }
    monitoringRecord.endTime = new Date();
    this.monitoringRecords.delete(symbol);

    let finishedRecords = this.finishedMonitoringRecords.get(symbol);
    if (!finishedRecords) {
      finishedRecords = [];
      this.finishedMonitoringRecords.set(symbol, finishedRecords);
    }
    finishedRecords.push(monitoringRecord);

    for (const handler of this.monitoringStopped) {
      handler(monitoringRecord);
    }
  }


  protected newSymbolUpdateRecord(): SymbolUpdateRecord {
    const symbolUpdateRecord = new SymbolUpdateRecord();
    symbolUpdateRecord.requestTime = new Date();
    return symbolUpdateRecord;
  }

  protected commitSymbolUpdateRecord(symbol: string, orderbook: ccxt.OrderBook | Error, symbolUpdateRecord: SymbolUpdateRecord) {
    const monitoringRecord = this.monitoringRecords.get(symbol);
    if (!monitoringRecord) {
      throw new Error(`Monitoring record for '${symbol}' is not found`);
    }

    symbolUpdateRecord.value = orderbook;
    symbolUpdateRecord.responseTime = new Date();
    monitoringRecord.updates.push(symbolUpdateRecord);

    for (const handler of this.monitoringUpdated) {
      handler(symbol, symbolUpdateRecord);
    }
  }

  protected notifySymbolUpdate(symbol: string, orderbook: ccxt.OrderBook | Error) {
    orderbookCache.updateRecord(this.exchange.id, symbol, orderbook);
    updateNotifier.notify(this.exchange.id, symbol, orderbook);
  }

  public async waitNextSuccessfulUpdate(symbol: string, ct = CancelToken.none): Promise<boolean> {
    if (ct.signaled) {
      return false;
    }

    const records = this.monitoringRecords.get(symbol);
    if (records?.getFirstSuccessfulUpdate()) {
      return true;
    }

    while (true) {
      const newUpdate = await this.waitNextUpdate(symbol, ct);
      if (ct.signaled) {
        return false;
      }

      if (newUpdate instanceof Error) {
        continue;
      }

      // logger.info(`waitNextSuccessfulUpdate '${symbol}' completed: ${'\t'}buy:${newUpdate.asks[0][0]}${'\t'}sell:${newUpdate.bids[0][0]}`);
      return true;
    }
  }

  public async waitNextUpdate(symbol: string, token = CancelToken.none)
    : Promise<ccxt.OrderBook | Error> {
    if (token.signaled) {
      return new Error('Cancelled');
    }

    const nextUpdateDeferred = new Deferred<ccxt.OrderBook | Error>();
    let isUnsubscribed = false;

    const nextUpdateCallback = (updateSymbol: string, updateRecord: SymbolUpdateRecord) => {
      if (isUnsubscribed) {
        return;
      }
      
      if(updateSymbol !== symbol) {
        return;
      }

      // on success
      isUnsubscribed = true;
      const index = this.monitoringUpdated.indexOf(nextUpdateCallback);
      if (index !== -1) {
        this.monitoringUpdated.splice(index, 1);
      }
      
      nextUpdateDeferred.resolve(updateRecord.value);
    };

    this.monitoringUpdated.push(nextUpdateCallback);

    const cancellationSubscription = token.subscribe(() => {
      if (isUnsubscribed) {
        return;
      }

      // on cancel
      isUnsubscribed = true;
      const index = this.monitoringUpdated.indexOf(nextUpdateCallback);
      if (index !== -1) {
        this.monitoringUpdated.splice(index, 1);
      }

      if (token.reason instanceof Error) {
        nextUpdateDeferred.resolve(token.reason);
        return;
      }

      nextUpdateDeferred.resolve(new Error('Cancelled'));
    });

    return nextUpdateDeferred.promise;
  }

  protected errorToDelay(error: Error) {
    if (error instanceof ccxt.ChecksumError) {
      return 1000;
    }

    if (error instanceof ccxt.OperationFailed) {
      return 3000;
    }

    if (error instanceof ccxt.ExchangeError) {
      return 10_000;
    }

    return 3000;
  }

  public async destroy(): Promise<void> {
    return;
  }
}