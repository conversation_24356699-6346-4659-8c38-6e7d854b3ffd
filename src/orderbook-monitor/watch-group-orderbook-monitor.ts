import * as ccxt from 'ccxt';
import { ExchangeError } from 'ccxt';
import WsClient from 'ccxt/js/src/base/ws/WsClient';
import { BaseOrderbookMonitor } from './base-orderbook-monitor';
import { exchanges } from '../core/exchanges-provider';
import { LINQ } from '../utils/linq';
import { logger } from '../utils/pinno-logger';
import { MonitoredSymbolsRegistry } from '../utils/monitored-symbols-registry';
import { getStartWatchScheduler, getStopWatchScheduler } from '../utils/exchange-task-sheduler';
import { ExchangeHelper } from '../utils/exchange-helper';

export class WatchGroupOrderbookMonitor extends BaseOrderbookMonitor {
  private static idCounter = 1;
  id: number = WatchGroupOrderbookMonitor.idCounter++;

  staticSymbols: string[];

  constructor(exchangeName: string, symbols: string[]) {
    super(exchangeName);
    this.subscribedSymbols = symbols;
  }

  override async initialize(): Promise<void> {
    await super.initialize();
    if (this.configs.groupMonitors_useSeparateExchange) {
      // coinbase only allows 30 symbols per connection and only 1 connection.
      // so we need to create a new exchange instance for each group.
      this.exchange = await exchanges.createExchange(this.exchangeName);
    }
  }

  public override subscribe(symbol: string): boolean {
    if (!LINQ.contains(this.staticSymbols, symbol)) {
      throw new Error(`'${symbol}' is not supported by this group (${this.id}).`);
    }

    // add the symbol to the subscribers list
    const result = super.subscribe(symbol);

    if (this.monitoringRecords.size === 0) {
      for (const symbol of this.staticSymbols) {
        this.startMonitoring(symbol);
      }

      this.loop(LINQ.toArray(this.staticSymbols))
        .catch((error) => {
          logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'loop' error:`);
        })
        .finally(() => {
          for (const symbol of this.staticSymbols) {
            this.stopMonitoring(symbol);
          }

          if (this.onMonitoringFinish != undefined) {
            this.onMonitoringFinish();
          }
        });
    }

    return result;
  }

  override async destroy(): Promise<void> {
    if (this.configs.groupMonitors_useSeparateExchange) {
      logger.info(`Closing exchange for WatchGroupOrderbookMonitor (${this.id})`);
      await this.exchange.close();
    }

    await super.destroy();
  }

  private loop: (symbols: string[]) => Promise<void> = this.getLoopMethod().bind(this);

  private getLoopMethod(): (symbols: string[]) => Promise<void> {
    const methodName = this.configs.groupMonitors_loopMethod;
    logger.info(`[${this.exchangeName}] WatchGroupOrderbookMonitor '${this.id}' use '${methodName}'`);
    return this[methodName] as (symbols: string[]) => Promise<void>;
  }

  private async normalUnwatchLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    await getStartWatchScheduler(this.exchange).schedule(symbols);
    while (this.subscribedSymbols.length > 0) {
      const orderbook = await this.watchSymbols(symbols);
    }
    await getStopWatchScheduler(this.exchange).schedule(symbols);
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    await this.unwatchSymbols(symbols);
  }

  private async closeConnectionLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    await getStartWatchScheduler(this.exchange).schedule(symbols);
    const beforeEstablishedConnectionClients = LINQ.toArray(Object.values(this.exchange.clients));

    // first update
    let orderbook: ccxt.OrderBook | Error = new Error('not successful start watching');
    while (orderbook instanceof Error) {
      orderbook = await this.watchSymbols(symbols);
    }

    const connectionHelper = ExchangeHelper.getConnectionsHelper(this.exchange);
    let client = connectionHelper.findClientServingSymbols(symbols, beforeEstablishedConnectionClients);
    if (client) {
      connectionHelper.stopClosingConnection(client, symbols);
    } else {
      throw new Error(`WatchGroupOrderbookMonitor '${this.id}' failed to find a client responsible for symbols`);
    }

    while (this.subscribedSymbols.length > 0) {
      const orderbook = await this.watchSymbols(symbols);
    }
    await getStopWatchScheduler(this.exchange).schedule(symbols);
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    await this.closeConnectionForSymbols(client, symbols);
  }

  private async forceUnwatchLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    await getStartWatchScheduler(this.exchange).schedule(symbols);
    while (this.subscribedSymbols.length > 0) {
      const orderbook = await this.watchSymbols(symbols);
    }
    await getStopWatchScheduler(this.exchange).schedule(symbols);
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);

    for (const symbol of symbols) {
      if (!MonitoredSymbolsRegistry.isMonitored(this.exchange, symbol)) {
        await this.unwatchSymbol(symbol);
      }
    }
  }

  private async closeExchangeLoop(symbols: string[]): Promise<void> {
    MonitoredSymbolsRegistry.register(this.exchange, symbols);
    await getStartWatchScheduler(this.exchange).schedule(symbols);
    while (this.subscribedSymbols.length > 0) {
      const orderbook = await this.watchSymbols(symbols);
    }
    await getStopWatchScheduler(this.exchange).schedule(symbols);
    MonitoredSymbolsRegistry.unregister(this.exchange, symbols);
  }

  private async unwatchSymbol(symbol: string): Promise<boolean> {
    try {
      const result = await this.exchange.unWatchOrderBook(symbol);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchGroupOrderbookMonitor '${this.id}' Failed to unwatch '${symbol}'`);
        return false;
      }
      logger.info(`WatchGroupOrderbookMonitor '${this.id}'. Unwatched '${symbol}'`);
      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchSymbol' '${symbol}' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async watchSymbols(symbols: string[]): Promise<ccxt.OrderBook | Error> {
    let updateRecord = this.newSymbolUpdateRecord();
    try {
      const orderbook = await this.exchange.watchOrderBookForSymbols(symbols);
      this.commitSymbolUpdateRecord(orderbook.symbol, orderbook, updateRecord);
      this.notifySymbolUpdate(orderbook.symbol, orderbook);
      return orderbook;
    } catch (error) {
      // if (error instanceof ExchangeClosedByUser) {
      //   // connection has closed, we should not watch these symbols anymore
      //   logger.warn(`WatchGroupOrderbookMonitor '${this.id}' connection closed, but we still watch it`);
      //   return error;
      // }

      const symbol = this.extractSymbolName(error);
      if (symbol) {
        if (error instanceof ccxt.ChecksumError) {
          // ChecksumError is a typical error, no need to log it. 
          // Usually it signalizes that server has high trading activity at the moment, 
          // and our app cannot keep up with updates during subscription phase, 
          // or node server is overloaded and cannot process updates fast enough.
          // Either way, our retry logic will handle it.
          logger.warn(`WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' '${symbol}' ChecksumError.`);
        } else {
          logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' '${symbol}' error:`);
        }

        this.commitSymbolUpdateRecord(symbol, error, updateRecord);
        this.notifySymbolUpdate(symbol, error);
      } else {
        logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'watchSymbols' error:`);
        for (const symbol of symbols) {
          this.commitSymbolUpdateRecord(symbol, error, updateRecord);
          this.notifySymbolUpdate(symbol, error);
        }
      }

      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return error;
    }
  }

  private extractSymbolName(err: ExchangeError): string | undefined {
    // {
    //   "name": "ChecksumError",
    //   "message": "binance DOGE/USDT : orderbook data checksum validation failed. 
    //               You can reconnect by calling watchOrderBook again, or you can mute the error by 
    //               setting exchange.options[\"watchOrderBook\"][\"checksum\"] = false"
    // }
    // Example regex: captures something like "DOGE/USDT" after "binance "
    const match = err.message.match(/\b([A-Z0-9]+\/[A-Z0-9]+)\b/);
    return match?.[1];
  }

  private async unwatchSymbols(symbols: string[]): Promise<boolean> {
    logger.info(`WatchGroupOrderbookMonitor '${this.id}' unwatching symbols: ${symbols.join(', ')}`);
    try {
      const result = await this.exchange.unWatchOrderBookForSymbols(symbols);
      if (typeof result === 'boolean' && !result) {
        logger.warn(`WatchGroupOrderbookMonitor '${this.id}' Failed to unwatch symbols`);
        return false;
      }

      return true;
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchRequests' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }

  private async closeConnectionForSymbols(client: WsClient, symbols: string[]): Promise<boolean> {
    try {
      return await ExchangeHelper.getConnectionsHelper(this.exchange).tryToCloseConnection(client, symbols);
    } catch (error) {
      logger.error(error, `WatchGroupOrderbookMonitor '${this.id}' 'unwatchRequests' error:`);
      const delay = this.errorToDelay(error);
      await new Promise(resolve => setTimeout(resolve, delay));
      return false;
    }
  }
}