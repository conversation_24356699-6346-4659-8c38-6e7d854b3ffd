import * as ccxt from 'ccxt';
import { logger } from './pinno-logger';
import { exchangeMonitoringConfigs } from '../core/exchange-monitoring-configs';

const exchangeStartWatchSchedulers = new Map<ccxt.Exchange, ExchangeTaskScheduler>();
export function getStartWatchScheduler(exchange: ccxt.Exchange): ExchangeTaskScheduler {
  let result = exchangeStartWatchSchedulers.get(exchange);
  if (!result) {
    const configs = exchangeMonitoringConfigs.get(exchange.id);
    result = new ExchangeTaskScheduler(exchange, "startWatch", configs.scheduler_startWatchMaxSymbolsPerSlot, configs.scheduler_startWatchSlotDelay);
    exchangeStartWatchSchedulers.set(exchange, result);
  }

  return result;
}

const exchangeStopWatchSchedulers = new Map<ccxt.Exchange, ExchangeTaskScheduler>();
export function getStopWatchScheduler(exchange: ccxt.Exchange): ExchangeTaskScheduler {
  let result = exchangeStopWatchSchedulers.get(exchange);
  if (!result) {
    const configs = exchangeMonitoringConfigs.get(exchange.id);
    result = new ExchangeTaskScheduler(exchange, "stopWatch", configs.scheduler_stopWatchMaxSymbolsPerSlot, configs.shedule_stopWatchSlotDelay);
    exchangeStopWatchSchedulers.set(exchange, result);
  }

  return result;
}


class ExchangeTaskScheduler {
  readonly exchange: ccxt.Exchange;
  readonly actionName: string;
  readonly stepDelay: number;
  readonly maxSymbolsPerStep: number;

  constructor(exchange: ccxt.Exchange, actionName: string, maxSymbolsPerSlot = 10, stepDelay = 5000) {
    this.actionName = actionName;
    this.exchange = exchange;
    this.maxSymbolsPerStep = maxSymbolsPerSlot;
    this.stepDelay = stepDelay;
  }

  public async schedule(symbols: string | string[]): Promise<void> {
    // avoid race condition when >10 symbols are added at the same time on bybit and maybe other exchanges
    const delay = this.getDelayBeforeSymbols(symbols);
    const symbolsStr = Array.isArray(symbols) ? `[${symbols.join(', ')}]` : `'${symbols}'`;
    if (delay > 0) {
      const scheduledTime = new Date(Date.now() + delay);
      logger.info(`${symbolsStr} ${this.actionName} is scheduled for [${this.HHmmssSSS(scheduledTime)}] (delay: ${delay}ms)`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    logger.info(`${symbolsStr} ${this.actionName}`);
  }

  // 21:00:39.446
  private HHmmssSSS(date: Date) {
    return `${String(date.getHours()).padStart(2, '0')}:` +
      `${String(date.getMinutes()).padStart(2, '0')}:` +
      `${String(date.getSeconds()).padStart(2, '0')}.` +
      `${String(date.getMilliseconds()).padStart(3, '0')}`;
  }

  private startTime = Date.now();

  // 500ms step │ queue of 10 symbols
  // 1          │ ethusdt, btcusdt
  // 2          │ [adausdt, dogeusdt, solusdt, arbusdt, dotusdt, trxusdt, avaxusdt, shibusdt]
  // 3          │ ltcusdt, linkusdt, [uniusdt, filusdt, icpusdt]
  private scheduledSlots = new Array<Array<string | string[]>>();

  public getDelayBeforeSymbols(symbols: string | string[]): number {
    //logger.info(`[SCHEDULER] ${symbols} - Before cleanup: slots=${this.scheduledSlots.length}, startTime=${this.startTime}`);
    this.removeOldEntries();

    //logger.info(`[SCHEDULER] ${symbols} - After cleanup: slots=${this.scheduledSlots.length}, startTime=${this.startTime}`);
    if (this.scheduledSlots.length === 0) {
      this.startTime = Date.now();
      //logger.info(`[SCHEDULER] ${symbols} - Reset startTime to ${this.startTime}`);
    }

    const slotIndex = this.findFirstAvailableSlot(symbols);
    if (slotIndex >= 0) {
      this.scheduledSlots[slotIndex].push(symbols);
      const delay = this.getTimeToStartSlot(slotIndex);
      //logger.info(`[SCHEDULER] ${symbols} - Using existing slot ${slotIndex}, delay=${delay}ms`);
      return delay;
    }

    const newSlot = [];
    newSlot.push(symbols);
    this.scheduledSlots.push(newSlot);
    const finalSlotIndex = this.scheduledSlots.length - 1;
    const delay = this.getTimeToStartSlot(finalSlotIndex);
    //logger.info(`[SCHEDULER] ${symbols} - New slot ${finalSlotIndex}, delay=${delay}ms`);
    return delay;
  }

  private removeOldEntries() {
    const totalSteps = this.scheduledSlots.length;
    const stepsPassed = Math.floor((Date.now() - this.startTime) / this.stepDelay);
    const stepsToRemove = Math.min(totalSteps, stepsPassed);
    //logger.info(`[SCHEDULER] removeOldEntries: totalSteps=${totalSteps}, stepsPassed=${stepsPassed}, stepsToRemove=${stepsToRemove}`);

    if (stepsToRemove > 0) {
      const removed = this.scheduledSlots.splice(0, stepsToRemove);
      this.startTime += stepsToRemove * this.stepDelay;
      //logger.info(`[SCHEDULER] Removed ${removed} slots, remaining: ${this.scheduledSlots.length}`);
    }
  }

  private findFirstAvailableSlot(symbols: string | string[]): number | undefined {
    const symbolsToAdd = Array.isArray(symbols) ? symbols.length : 1;

    for (let i = 0; i < this.scheduledSlots.length; i++) {
      let symbolsInSlot = 0;
      for (const symbol of this.scheduledSlots[i]) {
        symbolsInSlot += Array.isArray(symbol) ? symbol.length : 1;
      }

      if (symbolsInSlot + symbolsToAdd <= this.maxSymbolsPerStep) {
        return i;
      }
    }

    return undefined;
  }

  private getTimeToStartSlot(slotIndex: number): number {
    const scheduledTime = this.startTime + (slotIndex * this.stepDelay);
    return Math.max(0, scheduledTime - Date.now()); // time left to start the slot
  }
}
