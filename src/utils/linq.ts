type predicateType<T> = (item: T) => boolean; // returns true if the item matches the predicate
type selectorType<T, U> = (item: T) => U; // returns a new value based on the item  

export class LINQ {
  public static toArray<T>(source: Iterable<T>, array?: T[]): T[] {
    if (!array) {
      array = [];
    }
    for (const item of source) {
      array.push(item);
    }
    return array;
  }

  public static all<T>(source: Iterable<T>, predicate: predicateType<T>): boolean {
    for (const item of source) {
      if (!predicate(item)) {
        return false;
      }
    }
    return true;
  }

  public static any<T>(source: Iterable<T>, predicate?: predicateType<T>): boolean {
    if (predicate) {
      for (const item of source) {
        if (predicate(item)) {
          return true;
        }
      }
      return false;
    }
    for (const item of source) {
      return true;
    }
    return false;
  }

  // CAUTION! it's not sequential comparison. 
  // order of elements in the list from the source and in 'items' can be different, 
  // but they should be the same list of items eventually.
  public static containsList<T>(source: Iterable<T[]>, items: T[] | Set<T>): boolean {
    if (Array.isArray(items)) {
      for (const list of source) {
        if (list.length === items.length
          && list.every((symbol) => items.includes(symbol))) {
          return true;
        }
      }
      return false;
    } else {
      for (const list of source) {
        if (list.length === items.size
          && list.every((symbol) => items.has(symbol))) {
          return true;
        }
      }
      return false;
    }
  }

  public static contains<T>(source: Iterable<T>, value: T): boolean {
    for (const item of source) {
      if (item === value) {
        return true;
      }
    }
    return false;
  }

  // This method merges two lists into a single new list, 
  // preserving all elements from both lists, including duplicates. 
  // The order of elements from the first list is maintained, followed by the elements from the second list
  public static* concat<T>(source1: Iterable<T>, source2: Iterable<T>): IterableIterator<T> {
    for (const item of source1) {
      yield item;
    }
    for (const item of source2) {
      yield item;
    }
  }

  public static count<T>(source: Iterable<T>): number;
  public static count<T>(source: Iterable<T>, predicate: predicateType<T>): number;
  public static count<T>(source: Iterable<T>, value: T): number;
  public static count<T>(source: Iterable<T>, value?: T | predicateType<T>): number {
    // we use "arguments.length" to support counting "undefined" values, when user passes "undefined" as value.
    if (arguments.length === 1) {
      // no predicate
      let count = 0;
      for (const item of source) {
        count++;
      }
      return count;
    }
    if (typeof value === 'function') {
      const predicate = value as predicateType<T>;
      let count = 0;
      for (const item of source) {
        if (predicate(item)) {
          count++;
        }
      }
      return count;
    }
    let count = 0;
    for (const item of source) {
      if (item === value) {
        count++;
      }
    }
    return count;
  }

  // removes duplicates.
  // optional keySelector for structural comparison.
  public static* distinct<T, K>(source: Iterable<T>): IterableIterator<T> {
    const distinctItems = new Set<T>();
    for (const item of source) {
      if (!distinctItems.has(item)) {
        distinctItems.add(item);
        yield item;
      }
    }
    return;
  }

  // CAUTION! it's not sequential comparison. 
  // order of elements in list1 and list2 can be different, 
  // but if they should contain the same list of items, this method will return 'true'.
  public static equalList<T>(list1: T[] | Set<T>, list2: T[] | Set<T>): boolean {
    const list1Size = Array.isArray(list1) ? list1.length : list1.size;
    const list2Size = Array.isArray(list2) ? list2.length : list2.size;

    if (list1Size !== list2Size) {
      return false;
    }

    if (Array.isArray(list1)) {
      return LINQ.all(list2, (item) => list1.includes(item));
    } else {
      return LINQ.all(list2, (item) => list1.has(item));
    }
  }

  // excludes from source items that are in the values. 
  // optional keySelector for structural exclusion.
  public static* except<T>(source: Iterable<T>, values: T[] | Set<T>): IterableIterator<T> {
    if (Array.isArray(values)) {
      for (const item of source) {
        if (!values.includes(item)) {
          yield item;
        }
      }
    } else {
      for (const item of source) {
        if (!values.has(item)) {
          yield item;
        }
      }
    }
  }

  public static first<T>(source: Iterable<T>): T | undefined;
  public static first<T>(source: Iterable<T>, predicate: predicateType<T>): T | undefined;
  public static first<T>(source: Iterable<T>, predicate?: predicateType<T>): T | undefined {
    if (predicate === undefined) {
      for (const item of source) {
        return item;
      }
      return undefined;
    }
    for (const item of source) {
      if (predicate(item)) {
        return item;
      }
    }
    return undefined;
  }

  public static last<T>(source: Iterable<T>): T | undefined;
  public static last<T>(source: Iterable<T>, predicate: predicateType<T>): T | undefined;
  public static last<T>(source: Iterable<T>, predicate?: predicateType<T>): T | undefined {
    let lastItem: T | undefined;
    if (predicate === undefined) {
      for (const item of source) {
        lastItem = item;
      }
      return lastItem;
    }
    for (const item of source) {
      if (predicate(item)) {
        lastItem = item;
      }
    }
    return lastItem;
  }

  public static* select<T, U>(source: Iterable<T>, selector: selectorType<T, U>): IterableIterator<U> {
    for (const item of source) {
      yield selector(item);
    }
  }

  public static* selectMany<TCollection, TResult>(source: Iterable<TCollection>, collectionSelector: (item: TCollection) => Iterable<TResult>): IterableIterator<TResult> {
    for (const outer of source) {
      const innerIterable = collectionSelector(outer);
      for (const inner of innerIterable) {
        yield inner;
      }
    }
  }

  public static* take<T>(source: Iterable<T>, count: number): IterableIterator<T> {
    const n = Number.isFinite(count) ? Math.trunc(count) : 0;
    if (n <= 0) {
      return;
    }
    let remaining = n;
    for (const item of source) {
      yield item;
      remaining--;
      if (remaining === 0) {
        return;
      }
    }
  }

  public static* skip<T>(source: Iterable<T>, count: number): IterableIterator<T> {
    const n = Number.isFinite(count) ? Math.trunc(count) : 0;
    if (n <= 0) {
      for (const item of source) {
        yield item;
      }
      return;
    }
    let remaining = n;
    for (const item of source) {
      if (remaining > 0) {
        remaining--;
        continue;
      }
      yield item;
    }
  }

  // This method combines two lists and removes any duplicate elements, 
  // returning a new list containing only distinct elements from both sources.
  // Optional keySelector for structural comparison.
  public static* union<T, K>(source1: Iterable<T>, source2: Iterable<T>): IterableIterator<T> {
    const distinct = new Set<T>();
    for (const item of source1) {
      if (!distinct.has(item)) {
        distinct.add(item);
        yield item;
      }
    }
    for (const item of source2) {
      if (!distinct.has(item)) {
        distinct.add(item);
        yield item;
      }
    }
    return;
  }

  public static* where<T>(source: Iterable<T>, predicate: predicateType<T>): IterableIterator<T> {
    for (const item of source) {
      if (predicate(item)) {
        yield item;
      }
    }
  }

  public static min<T>(source: Iterable<T>, selector: selectorType<T, number>): number | undefined;
  public static min<T, U>(source: Iterable<T>, selector: selectorType<T, number>, selectorItem: selectorType<T, U>): U | undefined;
  public static min<T, U>(source: Iterable<T>, selector: selectorType<T, number>, selectorItem?: selectorType<T, U>): number | U | undefined {
    let min = Number.POSITIVE_INFINITY;
    let minItem: T | undefined;
    let hasItems = false;
    for (const item of source) {
      const value = selector(item);
      hasItems = true;
      if (value < min) {
        min = value;
        minItem = item;
      }
    }

    if (hasItems) {
      return selectorItem ? selectorItem(minItem) : min;
    }

    return undefined;
  }

  public static max<T, U>(source: Iterable<T>, selector: selectorType<T, number>, selectorItem?: selectorType<T, U>): number | U | undefined {
    let max = Number.NEGATIVE_INFINITY;
    let maxItem: T | undefined;
    let hasItems = false;
    for (const item of source) {
      const value = selector(item);
      hasItems = true;
      if (value > max) {
        max = value;
        maxItem = item;
      }
    }

    if (hasItems) {
      return selectorItem ? selectorItem(maxItem) : max;
    }

    return undefined;
  }

  public static avg<T>(source: Iterable<T>, selector: selectorType<T, number>, round = true): number | undefined {
    let sum = 0;
    let count = 0;
    for (const item of source) {
      sum += selector(item);
      count++;
    }

    if (count === 0) {
      return undefined;
    }

    return round
      ? Math.round(sum / count)
      : sum / count;
  }

  public static sum<T>(source: Iterable<T>, selector: selectorType<T, number>): number {
    let sum = 0;
    for (const item of source) {
      sum += selector(item);
    }
    return sum;
  }

  public static minMax<T>(source: Iterable<T>, selector: selectorType<T, number>): {
    count: number,
    min: number | undefined,
    max: number | undefined
  } {
    let min = Number.POSITIVE_INFINITY;
    let max = Number.NEGATIVE_INFINITY;
    let count = 0;
    for (const item of source) {
      const value = selector(item);
      if (value < min) {
        min = value;
      }
      if (value > max) {
        max = value;
      }
      count++;
    }

    if (count === 0) {
      return { count: 0, min: undefined, max: undefined };
    }

    return { count, min, max };
  }

  public static minMaxAvg<T>(source: Iterable<T>, selector: selectorType<T, number>, round = true): {
    count: number,
    min: number | undefined,
    max: number | undefined,
    avg: number | undefined
  } {
    let min = Number.POSITIVE_INFINITY;
    let max = Number.NEGATIVE_INFINITY;
    let sum = 0;
    let count = 0;
    for (const item of source) {
      const value = selector(item);
      if (value < min) {
        min = value;
      }
      if (value > max) {
        max = value;
      }
      sum += value;
      count++;
    }

    if (count === 0) {
      return { count: 0, min: undefined, max: undefined, avg: undefined };
    }

    const avg = round
      ? Math.round(sum / count)
      : sum / count;

    return { count, min, max, avg };
  }
}









