import { logger } from './pinno-logger';

export function hookMethod<T extends object, K extends keyof T>(
  obj: T,
  methodName: K
): void {
  const originalMethod = obj[methodName];

  if (typeof originalMethod === 'function') {
    obj[methodName] = function (...args: any[]) {
      logger.info(`'${String(methodName)}'(${args})`);
      const result = (originalMethod as Function).apply(this, args);
      //console.log(`[LOG] ${String(methodName)} returned:`, result);
      return result;
    } as T[K];
  }
}