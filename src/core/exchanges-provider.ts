import * as ccxt from 'ccxt';
import { exchangeMonitoringConfigs } from './exchange-monitoring-configs';

interface EnvConfigs {
  apiKey: string;
  secret: string;
  password?: string;
}

export class ExchangesProvider {
  private exchangeConfigs: Map<string, EnvConfigs> = new Map<string, EnvConfigs>();
  private initializedExchanges = new Map<string, ccxt.Exchange>();

  constructor(jsonConfigs: string, singleExchangeName?: string) {
    const objConfigs = JSON.parse(jsonConfigs);// ["exchangeName", EnvConfigs]

    if (singleExchangeName) {
      this.exchangeConfigs.set(singleExchangeName, objConfigs[singleExchangeName]);
    } else {
      for (const exchangeName in objConfigs) {
        this.exchangeConfigs.set(exchangeName, objConfigs[exchangeName]);
      }
    }
  }

  public async createAllExchanges(): Promise<void> {
    const promises: Promise<ccxt.Exchange>[] = [];
    for (const [exchangeName, envConfig] of this.exchangeConfigs) {
      promises.push(this.createExchange(exchangeName, envConfig));
    }

    const exchanges = await Promise.all(promises);
    for (const exchange of exchanges) {
      this.initializedExchanges.set(exchange.id, exchange);
    }
  }

  public async createExchange(exchangeName: string, envConfig?: EnvConfigs): Promise<ccxt.Exchange> {
    const exchangeClass = (ccxt.pro)[exchangeName];
    if (!exchangeClass) {
      throw new ccxt.NotSupported(`Exchange ${exchangeName} not supported`);
    }

    if (!envConfig) {
      envConfig = this.exchangeConfigs.get(exchangeName);
    }

    if (!envConfig) {
      throw new ccxt.NotSupported(`Missing configurations for ${exchangeName}`);
    }

    const userConfig = {
      envConfig
    };

    const exchange: ccxt.Exchange = new exchangeClass(userConfig);
    exchange.options['maxRetriesOnFailure'] = 0; // we don't need retry logic, because we'll retry it in the next loop cycle anyway (BaseExchangeMonitor)
    exchange.rateLimit = exchangeMonitoringConfigs.get(exchangeName).fetchMonitor_fetchOrderbookInterval;
    exchange.enableRateLimit = true;
    exchange.options['watchOrderBook'] = {
      'maxRetries': 5, // Increase from default 3 to 5 attempts
      'snapshotMaxRetries': 6,
      'snapshotDelay': 1000, // Add 1-second delay before snapshot requests
    };

    // Special handling for MEXC to ensure protobuf is properly loaded
    if (exchangeName === 'mexc') {
      await this.ensureMexcProtobufLoaded(exchange);
    }

    const markets = await exchange.loadMarkets();
    exchange.verbose = true;
    return exchange;
  }

  private async ensureMexcProtobufLoaded(exchange: ccxt.Exchange): Promise<void> {
    try {
      // Test if protobuf is already working
      const testBuffer = new Uint8Array([0x0a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f]);
      exchange.decodeProtoMsg(testBuffer);
      // If we get here, protobuf is working
      return;
    } catch (error) {
      if (error.message.includes('requires protobuf to decode messages')) {
        // Load protobuf manually and override the decodeProtoMsg method
        try {
          const path = require('path');
          const protobufPath = path.join(process.cwd(), 'node_modules/ccxt/js/src/protobuf/mexc/compiled.cjs');
          const protobufMexc = require(protobufPath);

          // Override the decodeProtoMsg method with a working implementation
          exchange.decodeProtoMsg = function(data: any) {
            if (data instanceof Uint8Array) {
              const decoded = protobufMexc.PushDataV3ApiWrapper.decode(data);
              const dict = decoded.toJSON();
              return dict;
            }
            return data;
          };

          // Test the fix
          const testBuffer2 = new Uint8Array([0x0a, 0x05, 0x68, 0x65, 0x6c, 0x6c, 0x6f]);
          exchange.decodeProtoMsg(testBuffer2);
          console.log('✅ MEXC protobuf loading fixed successfully');

        } catch (fixError) {
          console.error('❌ Failed to fix MEXC protobuf loading:', fixError.message);
          throw new ccxt.NotSupported(`MEXC protobuf loading failed: ${fixError.message}`);
        }
      } else {
        // Some other error, re-throw it
        throw error;
      }
    }
  }

  public get(exchangeName: string): ccxt.Exchange {
    return this.initializedExchanges.get(exchangeName);
  }

  public async getOrCreate(exchangeName: string): Promise<ccxt.Exchange> {
    const getResult = this.get(exchangeName);
    if (getResult) {
      return getResult;
    }

    const exchange = await this.createExchange(exchangeName);
    this.initializedExchanges.set(exchange.id, exchange);
    return exchange;
  }

  public all(): Map<string, ccxt.Exchange> {
    return this.initializedExchanges;
  }
}

export const exchanges = new ExchangesProvider(process.env.EXCHANGES, process.env.SINGLE_EXCHANGE_NAME);