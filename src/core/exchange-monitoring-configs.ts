import { logger } from '../utils/pinno-logger';
import { exchanges } from './exchanges-provider';

export class ExchangeMonitoringConfig {
  fetchMonitor_enabled: boolean = true;
  // how long to wait before fetching orderbook for a new symbol
  fetchMonitor_fetchOrderbookInterval: number = 1000;

  // max number of individual monitors that can be created
  individualMonitors_count: number = 1;
  // how many symbols can be monitored by individual monitor
  individualMonitors_maxSymbols: number = 20;
  // what loop method to use for individual monitors
  individualMonitors_LoopMethod: 'normalUnwatchLoop' | 'forceUnwatchLoop' | 'closeConnectionLoop' | 'closeExchangeLoop' = 'normalUnwatchLoop';
  individualMonitors_useSeparateExchange: boolean = false;

  // how many symbols should be used to create preinitialized groups
  groupMonitors_preinitializedTopSymbols: number = 60;
  // how many group monitors should be used to handle preinitializedTopVolumeSymbolsCount (recommendation for balancer, but not a guaranteed value)
  groupMonitors_preinitializedMonitorCount: number = 2;
  // max number of group monitors that can be created
  groupMonitors_count: number = Number.POSITIVE_INFINITY;
  // what loop method to use for individual monitors
  groupMonitors_loopMethod: 'normalUnwatchLoop' | 'forceUnwatchLoop' | 'closeConnectionLoop' | 'closeExchangeLoop' = 'normalUnwatchLoop';
  groupMonitors_useSeparateExchange: boolean = false;
  // max number of symbols that can be monitored by one group monitor
  groupMonitors_maxSymbols: number = 10;

  rebalance_interval: number = 60_000;
  // how many symbols needed to create a new group
  rebalance_minSymbolsToCreateNewGroup: number = 10;
  // how many symbols needed to start monitoring existing group
  rebalance_minSymbolsToStartGroup: number = 3;

  // how many symbols can be started watching at the same time
  scheduler_startWatchMaxSymbolsPerSlot: number = 10;
  // how many ms to wait before starting watching next slot
  scheduler_startWatchSlotDelay: number = 1000;

  // how many symbols can be started watching at the same time
  scheduler_stopWatchMaxSymbolsPerSlot: number = 5;
  // how many ms to wait before starting watching next slot
  shedule_stopWatchSlotDelay: number = 2000;



  public static fromOverrides(overrides: Partial<ExchangeMonitoringConfig>): ExchangeMonitoringConfig {
    return Object.assign(new ExchangeMonitoringConfig(), overrides);
  }
}

export const exchangeMonitoringConfigs = new Map<string, ExchangeMonitoringConfig>([
  // {"fetchOrderBook":true,"fetchOrderBooks":false,
  // "watchOrderBook":true,"unWatchOrderBook":false,
  // "watchOrderBookForSymbols":true,"unWatchOrderBookForSymbols":false}
  ['binance', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_maxSymbols: 150,
    individualMonitors_LoopMethod: 'closeConnectionLoop',
    groupMonitors_loopMethod: 'closeConnectionLoop',
    groupMonitors_preinitializedMonitorCount: 4,
  })],

  // {"fetchOrderBook":true,"fetchOrderBooks":false,
  // "watchOrderBook":true,"unWatchOrderBook":true,
  // "watchOrderBookForSymbols":true,"unWatchOrderBookForSymbols":true}
  ['bybit', ExchangeMonitoringConfig.fromOverrides({
    // maxSymbolsPerIndividualMonitor: 30,
    individualMonitors_count: Number.POSITIVE_INFINITY,
    individualMonitors_useSeparateExchange: true,
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_loopMethod: 'forceUnwatchLoop',
  })],

  // {"fetchOrderBook":true,"fetchOrderBooks":false,
  // "watchOrderBook":true,"unWatchOrderBook":false,
  // "watchOrderBookForSymbols":true,"unWatchOrderBookForSymbols":false}
  ['coinbase', ExchangeMonitoringConfig.fromOverrides({
    fetchMonitor_fetchOrderbookInterval: 2000,
    rebalance_interval: 30_000,
    //individualMonitorCount: 0,
    individualMonitors_count: Number.POSITIVE_INFINITY,
    individualMonitors_useSeparateExchange: true,
    individualMonitors_LoopMethod: 'closeExchangeLoop',

    groupMonitors_loopMethod: 'closeExchangeLoop',
    groupMonitors_useSeparateExchange: true,
    groupMonitors_preinitializedTopSymbols: 0,
    groupMonitors_maxSymbols: 5,
  })],

  ['okx', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_count: 1,
    individualMonitors_maxSymbols: 150,
    individualMonitors_LoopMethod: 'forceUnwatchLoop',
    groupMonitors_loopMethod: 'forceUnwatchLoop',
    groupMonitors_preinitializedTopSymbols: 0,
  })],

  // {"fetchOrderBook":true,"fetchOrderBooks":false,
  // "watchOrderBook":true,"unWatchOrderBook":false,
  // "watchOrderBookForSymbols":true,"unWatchOrderBookForSymbols":false}
  ['bitget', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_count: 1,
    individualMonitors_maxSymbols: 150,
    individualMonitors_LoopMethod: 'forceUnwatchLoop',

    groupMonitors_loopMethod: 'closeExchangeLoop',
    groupMonitors_useSeparateExchange: true,
    groupMonitors_preinitializedTopSymbols: 0,
  })],

  // {"fetchOrderBook":true,"fetchOrderBooks":false,
  // "watchOrderBook":true,"unWatchOrderBook":true,
  // "watchOrderBookForSymbols":false,"unWatchOrderBookForSymbols":false}
  ['mexc', ExchangeMonitoringConfig.fromOverrides({
    fetchMonitor_enabled: false,
    // individualMonitors_count: Number.POSITIVE_INFINITY,
    // individualMonitors_LoopMethod: 'closeExchangeLoop',
    // individualMonitors_useSeparateExchange: true,
    individualMonitors_count: 1,
    individualMonitors_maxSymbols: 150,
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_count: 0,
  })],
  ['gate', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_maxSymbols: 150,
    individualMonitors_LoopMethod: 'forceUnwatchLoop',
    groupMonitors_count: 0,
  })],

  ['htx', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_count: Number.POSITIVE_INFINITY,
    individualMonitors_maxSymbols: 3,
    individualMonitors_LoopMethod: 'closeExchangeLoop',
    individualMonitors_useSeparateExchange: true,
    groupMonitors_count: 0,
    scheduler_startWatchMaxSymbolsPerSlot: 1,
    scheduler_startWatchSlotDelay: 3000,
    rebalance_interval: 30_000,
  })],

  ['kucoin', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_count: Number.POSITIVE_INFINITY,
    individualMonitors_maxSymbols: 15,
    individualMonitors_LoopMethod: 'closeExchangeLoop',
    individualMonitors_useSeparateExchange: true,
    groupMonitors_count: 0,
    scheduler_startWatchMaxSymbolsPerSlot: 5,
    scheduler_startWatchSlotDelay: 3000,
    scheduler_stopWatchMaxSymbolsPerSlot: 5,
    shedule_stopWatchSlotDelay: 3000,
  })],

  ['kraken', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_loopMethod: 'normalUnwatchLoop',
  })],

  ['bitfinex', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_loopMethod: 'normalUnwatchLoop',
  })],

  ['bitmart', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_loopMethod: 'normalUnwatchLoop',
  })],

  ['exmo', ExchangeMonitoringConfig.fromOverrides({
    individualMonitors_LoopMethod: 'normalUnwatchLoop',
    groupMonitors_loopMethod: 'normalUnwatchLoop',
  })]
]);

function validateConfig(exchangeName: string, config: ExchangeMonitoringConfig): Error[] {
  const result: Error[] = [];
  if (config.groupMonitors_count > 0
    && config.groupMonitors_maxSymbols > config.scheduler_startWatchMaxSymbolsPerSlot) {
    // in best case it will just look odd, that for example we limit it to start monitoring 1 individual symbol per second,
    // but then start 30 symbols in a group monitor in one go.
    // in worst case it may request to start monitoring more symbols than it's allowed to, and this may lead to exchange errors.
    result.push(new Error(`'startWatchMaxSymbolsPerSlot' must be >= 'maxSymbolsPerGroupMonitor'`));
  }

  if (config.groupMonitors_loopMethod === 'closeExchangeLoop'
    && config.groupMonitors_preinitializedTopSymbols > 0) {
    // using "closeExchangeLoop" likely means that normal stopping of group monitors is not possible, 
    // so after creating and starting preinitialized groups they will not be stopped and continue monitoring even when they are not needed.
    result.push(new Error(`when using 'closeExchangeLoop' on group monitors, it's not recommended to use preinitialized groups`));
  }

  if (config.individualMonitors_count > 1 && config.individualMonitors_useSeparateExchange === false) {
    // using a single exchange instance for multiple individual monitors may cause endless watchOrderbook calls issue.
    // besides, if it works fine with a single connection, why use multiple individual monitors at all?
    result.push(new Error(`using a shared exchange instance for multiple individual monitors is not recommended`));
  }

  if (config.groupMonitors_preinitializedTopSymbols > 100) {
    // we don't fetch more than 100 symbols
    result.push(new Error(`'preinitializedTopVolumeSymbolsCount' is too high`));
  }

  const exchange = exchanges.get(exchangeName);
  if (!exchange) {
    return result;
  }

  if (config.individualMonitors_count > 0) {
    if (!exchange.has['watchOrderBook']) {
      result.push(new Error(`exchange does not support 'watchOrderBook', but individual monitors are used.`));
    }
    if (!exchange.has['unWatchOrderBook'] && config.individualMonitors_LoopMethod === 'normalUnwatchLoop') {
      result.push(new Error(`exchange does not support 'unWatchOrderBook', but 'normalUnwatchLoop' is used.`));
    }
    if (exchange.has['unWatchOrderBook'] && config.individualMonitors_LoopMethod === 'forceUnwatchLoop') {
      result.push(new Error(`'forceUnwatchLoop' is not recommended when exchange supports 'unWatchOrderBook'`));
    }
  }

  if (config.groupMonitors_count > 0) {
    if (!exchange.has['watchOrderBookForSymbols']) {
      result.push(new Error(`exchange does not support 'watchOrderBookForSymbols', but group monitors are used.`));
    }
    if (!exchange.has['unwatchOrderBookForSymbols'] && config.groupMonitors_loopMethod === 'normalUnwatchLoop') {
      result.push(new Error(`exchange does not support 'unwatchOrderBookForSymbols', but 'normalUnwatchLoop' is used.`));
    }
    if (exchange.has['unwatchOrderBookForSymbols'] && config.groupMonitors_loopMethod === 'forceUnwatchLoop') {
      result.push(new Error(`'forceUnwatchLoop' is not recommended when exchange supports 'unwatchOrderBookForSymbols'`));
    }
  }

  return result;
}

export function validateAllConfigs() {
  for (const [exchangeName, config] of exchangeMonitoringConfigs) {
    const errors = validateConfig(exchangeName, config);
    if (errors.length > 0) {
      logger.error(`Monitoring configs validation failed for '${exchangeName}':`);
      for (const error of errors) {
        logger.error(error);
      }
    }
  }
}