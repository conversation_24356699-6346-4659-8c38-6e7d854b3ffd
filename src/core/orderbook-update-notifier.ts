import * as ccxt from 'ccxt';
import { CancelToken } from '@esfx/canceltoken';
import { Deferred } from '@esfx/async-deferred';

export type DataUpdatedHandler = (newOrderBookValue: ccxt.OrderBook | Error) => void;

export class OrderbookUpdateNotifier {
  orderbookUpdateSubscribers = new Map<string, Map<string, DataUpdatedHandler[]>>();

  public subscribe(exchangeId: string, symbol: string, eventHandler: DataUpdatedHandler) {
    let exchangeSubscribers = this.orderbookUpdateSubscribers.get(exchangeId);
    if (!exchangeSubscribers) {
      exchangeSubscribers = new Map<string, DataUpdatedHandler[]>;
      this.orderbookUpdateSubscribers.set(exchangeId, exchangeSubscribers);
    }

    let symbolSubscribers = exchangeSubscribers.get(symbol);
    if (!symbolSubscribers) {
      symbolSubscribers = [];
      exchangeSubscribers.set(symbol, symbolSubscribers);
    }

    symbolSubscribers.push(eventHandler);
  }

  public unsubscribe(exchangeId: string, symbol: string, eventHandler: DataUpdatedHandler): boolean {
    let exchangeSubscribers = this.orderbookUpdateSubscribers.get(exchangeId);
    if (!exchangeSubscribers) {
      return false;
    }

    let symbolSubscribers = exchangeSubscribers.get(symbol);
    if (!symbolSubscribers) {
      return false;
    }

    const index = symbolSubscribers.indexOf(eventHandler);
    if (index !== -1) {
      symbolSubscribers.splice(index, 1);
    }

    return true;
  }

  public notify(exchangeId: string, symbol: string, newOrderBookValue: ccxt.OrderBook | Error) {
    const exchangeSubscribers = this.orderbookUpdateSubscribers.get(exchangeId);
    if (!exchangeSubscribers) {
      return false;
    }

    const symbolSubscribers = exchangeSubscribers.get(symbol);
    if (!symbolSubscribers) {
      return false;
    }

    // let symbolSubscribers = exchangeSubscribers.get(symbol);
    // if (!symbolSubscribers) {
    //   const symbols = symbol.split('/');
    //   if(symbols.length !== 2) {
    //     return false;
    //   }
    //   symbol = symbols[0] + '/'+ "USD";
    //   let symbolSubscribers = exchangeSubscribers.get(symbol);
    //   if (!symbolSubscribers) {
    //     return false;
    //   }
    // }

    for (const handler of symbolSubscribers) {
      try {
        handler(newOrderBookValue);
      } catch (error) {
        console.error(`notifySymbolUpdate for ${exchangeId}:${symbol} error:`, error);
      }
    }

    return true;
  }

  public async waitNextUpdate(exchangeId: string, symbol: string, token = CancelToken.none)
    : Promise<ccxt.OrderBook | Error> {
    if (token.signaled) {
      return new Error('Cancelled');
    }

    const nextUpdateDeferred = new Deferred<ccxt.OrderBook | Error>();
    let isUnsubscribed = false;

    const nextUpdateCallback: DataUpdatedHandler = (orderbook: ccxt.OrderBook | Error) => {
      if (isUnsubscribed) {
        return;
      }

      // on success
      isUnsubscribed = true;
      this.unsubscribe(exchangeId, symbol, nextUpdateCallback);
      nextUpdateDeferred.resolve(orderbook);
    };

    this.subscribe(exchangeId, symbol, nextUpdateCallback);

    const cancellationSubscription = token.subscribe(() => {
      if (isUnsubscribed) {
        return;
      }

      // on cancel
      isUnsubscribed = true;
      this.unsubscribe(exchangeId, symbol, nextUpdateCallback);

      if (token.reason instanceof Error) {
        nextUpdateDeferred.resolve(token.reason);
        return;
      }

      nextUpdateDeferred.resolve(new Error('Cancelled'));
    });

    return nextUpdateDeferred.promise;
  }
}

export const updateNotifier = new OrderbookUpdateNotifier();