import * as ccxt from 'ccxt';

// We need this storage because 'exchange.orderbooks' is not updated after 'fetchOrderbook(s)' methods calls. 
// So here we have a single consistent store for all orderbook update methods.
class OrderbookCache {
  // 2 ways to represent the same data.
  // access it as it is convenient.
  exchangesSymbolsOrderbooks = new Map<string, Map<string, OrderbookRecord>>();
  symbolsExchangesOrderbooks = new Map<string, Map<string, OrderbookRecord>>();

  public updateRecord(exchangeId: string, symbol: string, orderbook: ccxt.OrderBook | Error) {
    const symbolOrderbook = this.getOrderbooksOfExchange(exchangeId);

    // keep the same record object for both maps 
    // to avoid comparison issues and speed up storage updates.
    let record = symbolOrderbook.get(symbol);
    if (!record) {
      const exchangeOrderbook = this.getOrderbooksOfSymbol(symbol);
      // once record is created, it remains in the maps forever.
      record = { orderbook, lastUpdate: Date.now() };
      symbolOrderbook.set(symbol, record);
      exchangeOrderbook.set(exchangeId, record);
      return;
    }

    // you don't need to set it into maps after the update.
    record.orderbook = orderbook;
    record.lastUpdate = Date.now();
  }

  // returns map<symbol, OrderbookRecord>
  public getOrderbooksOfExchange(exchangeId: string): Map<string, OrderbookRecord> {
    let result = this.exchangesSymbolsOrderbooks.get(exchangeId);
    if (!result) {
      result = new Map<string, OrderbookRecord>();
      this.exchangesSymbolsOrderbooks.set(exchangeId, result);
    }

    return result;
  }

  // returns map<exchangeId, OrderbookRecord>
  public getOrderbooksOfSymbol(symbol: string): Map<string, OrderbookRecord> {
    let result = this.symbolsExchangesOrderbooks.get(symbol);
    if (!result) {
      result = new Map<string, OrderbookRecord>();
      this.symbolsExchangesOrderbooks.set(symbol, result);
    }

    return result;
  }

  public getRecord(exchangeId: string, symbol: string): OrderbookRecord {
    return this.getOrderbooksOfExchange(exchangeId).get(symbol);
  }
}

export interface OrderbookRecord {
  orderbook: ccxt.OrderBook | Error,
  lastUpdate: number;
}

export const orderbookCache = new OrderbookCache();