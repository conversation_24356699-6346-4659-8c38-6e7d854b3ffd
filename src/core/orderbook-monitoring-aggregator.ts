import { ExchangesProvider } from './exchanges-provider';
import { MonitoredRequest } from './monitored-request';
import { MonitoringBalancer } from './monitoring-balancer';

export class OrderbookMonitoringAggregator {
  monitoringBalancers: MonitoringBalancer[] = [];

  public async initialize(exchanges: ExchangesProvider): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (const [exchangeName, exchange] of exchanges.all()) {
      const watcher = new MonitoringBalancer(exchangeName);
      this.monitoringBalancers.push(watcher);
      promises.push(watcher.initialize());
    }
    
    await Promise.all(promises);
  }

  public start(item: MonitoredRequest): boolean {
    const watcher = this.getOrderbookMonitor(item.exchangeName);
    const wasTheseSymbolsMonitored = watcher.isMonitored(item.symbols);
    watcher.subscribe(item);
    return wasTheseSymbolsMonitored;
  }

  public stop(item: MonitoredRequest) {
    const watcher = this.getOrderbookMonitor(item.exchangeName);
    if (!watcher) {
      return;
    }
    watcher.unsubscribe(item);
  }

  public getOrderbookMonitor(exchangeName: string): MonitoringBalancer {
    for (const watcher of this.monitoringBalancers) {
      if (watcher.exchange.id === exchangeName) {
        return watcher;
      }
    }

    return undefined;
  }
}

export const monitoring = new OrderbookMonitoringAggregator();
