import * as ccxt from 'ccxt';
import os from 'node:os';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from './monitored-request';
import { LINQ } from '../utils/linq';
import { ExchangeHelper } from '../utils/exchange-helper';
import { ExchangeMonitoringConfig, exchangeMonitoringConfigs } from './exchange-monitoring-configs';
import { exchanges } from './exchanges-provider';
import { WatchGroupOrderbookMonitor } from '../orderbook-monitor/watch-group-orderbook-monitor';
import { FetchIndividualOrderbookMonitor } from '../orderbook-monitor/fetch-individual-orderbook-monitor';
import { WatchIndividualExchangeMonitor } from '../orderbook-monitor/watch-individual-exchange-monitor';
import { RebalanceTransition } from '../orderbook-monitor/helpers/rebalance-transition';
import { SymbolUpdateRecord } from '../orderbook-monitor/helpers/symbol-monitoring-record';

export class MonitoringBalancer {
  exchangeName: string;
  exchange: ccxt.Exchange;
  configs: ExchangeMonitoringConfig;
  protected symbolSubscribers = new Map<string, Set<MonitoredRequest>>();

  groupMonitors: WatchGroupOrderbookMonitor[];
  individualMonitors: WatchIndividualExchangeMonitor[];
  fetchMonitor: FetchIndividualOrderbookMonitor;

  private topTickers: ccxt.Ticker[];

  constructor(exchangeName: string) {
    this.exchangeName = exchangeName;
    this.exchange = exchanges.get(exchangeName);
    this.configs = exchangeMonitoringConfigs.get(exchangeName);
    this.notifySubscribers = this.notifySubscribers.bind(this);
  }

  public async initialize(): Promise<void> {
    this.topTickers = await ExchangeHelper.getTopTickersByVolume(this.exchange, 100);

    if (this.configs.fetchMonitor_enabled) {
      this.fetchMonitor = new FetchIndividualOrderbookMonitor(this.exchange.id);
      this.fetchMonitor.requestsInterval = this.configs.fetchMonitor_fetchOrderbookInterval;
      this.fetchMonitor.monitoringUpdated.push(this.notifySubscribers);
      await this.fetchMonitor.initialize();
    }

    if (this.configs.individualMonitors_count > 0) {
      this.individualMonitors = [];

      if (this.configs.individualMonitors_count === 1) {
        const monitor = await this.addNewIndividualMonitor();
        if (this.configs.individualMonitors_useSeparateExchange) {
          monitor.onMonitoringFinish = () => this.removeIndividualMonitor(monitor);
        }
      }
    }

    if (this.configs.groupMonitors_count > 0) {
      this.groupMonitors = [];

      if (this.configs.groupMonitors_preinitializedTopSymbols > 0) {
        const tickers = this.topTickers.slice(0, this.configs.groupMonitors_preinitializedTopSymbols);
        const watchersToCreate = this.configs.groupMonitors_preinitializedMonitorCount;
        const maxSymbolsPerGroup = this.configs.groupMonitors_maxSymbols;

        for (const groupedTickers of ExchangeHelper.groupSymbolsByActivity(tickers, watchersToCreate, maxSymbolsPerGroup)) {
          const symbols = groupedTickers.symbols.map(ticket => ticket.symbol);
          await this.addNewGroupMonitor(symbols);
        }
      }
    }

    // setInterval(()=>this.closeNonUsedExchanges().catch((error) =>
    //     logger.error(error, 'BalancerOrderbookMonitor closeNonUsedExchanges error:'))
    //   , 30_000);

    this.rebalanceLoop().catch((error) =>
      logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:'));
  }

  // sometimes we get ws connection errors for unknown reasons 
  // (most likely, due to the bad connection or high exchange api load).
  // we need this method to clean up the failed connections and thus restore the exchange instance.
  async closeNonUsedExchanges(): Promise<void> {
    if (Object.keys(this.exchange.clients).length === 0) {
      return;
    }

    if (this.configs.groupMonitors_count > 0
      && this.configs.groupMonitors_useSeparateExchange === false
      && LINQ.any(this.groupMonitors, (monitor) => LINQ.any(monitor.getMonitoredSymbols()))) {
      // do not close connections, if there are active group monitors
      return;
    }

    if (this.configs.individualMonitors_count === 1
      && this.configs.individualMonitors_useSeparateExchange === false
      && LINQ.any(this.individualMonitors[0].getMonitoredSymbols())) {
      // do not close connections, if there are any active individual monitoring loops
      return;
    }

    logger.info(`Closing shared '${this.exchange.id}' exchange due to no active monitoring`);
    await this.exchange.close();
  }

  private async addNewGroupMonitor(symbols: string[]): Promise<WatchGroupOrderbookMonitor> {
    const monitor = new WatchGroupOrderbookMonitor(this.exchange.id, symbols.sort());
    monitor.monitoringUpdated.push(this.notifySubscribers);
    await monitor.initialize();
    this.groupMonitors.push(monitor);
    return monitor;
  }

  private async addNewIndividualMonitor(): Promise<WatchIndividualExchangeMonitor> {
    const monitor = new WatchIndividualExchangeMonitor(this.exchange.id);
    monitor.monitoringUpdated.push(this.notifySubscribers);
    await monitor.initialize();
    this.individualMonitors.push(monitor);
    return monitor;
  }

  public notifySubscribers(symbol: string, updateRecord: SymbolUpdateRecord) {
    const subscribers = this.symbolSubscribers.get(symbol);
    if (!subscribers) {
      return;
    }

    // notify monitored requests if there are any still
    for (const monitoredRequest of subscribers) {
      monitoredRequest.updateData(updateRecord.value);
    }
  }

  public subscribe(item: MonitoredRequest): boolean {
    let subscribers = this.symbolSubscribers.get(item.symbols);
    if (!subscribers) {
      subscribers = new Set<MonitoredRequest>();
      this.symbolSubscribers.set(item.symbols, subscribers);
    }

    if (subscribers.has(item)) {
      return false;
    }

    subscribers.add(item);

    if (this.fetchMonitor?.isMonitored(item.symbols)) {
      return true;
    }

    if (this.individualMonitors !== undefined && LINQ.any(this.individualMonitors,
      (monitor) => monitor.hasReceivedFirstSuccessfulUpdate(item.symbols))) {
      return true;
    }

    if (this.groupMonitors !== undefined && LINQ.any(this.groupMonitors,
      (monitor) => monitor.hasReceivedFirstSuccessfulUpdate(item.symbols))) {
      return true;
    }

    if (!this.startMonitoring(item.symbols)) {
      logger.error(`Failed to start monitoring '${item.symbols}' on ${this.exchange.id}`);
    }

    return true;
  }

  private notUpgradedSymbols: string[] = [];
  private upgradeTransitions: RebalanceTransition[] = [];
  private downgradeTransitions: RebalanceTransition[] = [];

  private startMonitoring(symbol: string): boolean {
    logger.info(`balancer startMonitoring '${symbol}'`);

    let monitoringStarted = false;
    if (this.fetchMonitor !== undefined) {
      // start fetching it periodically.
      // this provides user with a stream of slow updates, but quickly, making API more responsive.
      const result = this.fetchMonitor.subscribe(symbol);
      monitoringStarted ||= result;
    }

    let individualMonitor: WatchIndividualExchangeMonitor | undefined = undefined;
    if (this.individualMonitors !== undefined) {
      // check if symbol is already being monitored by an individual monitor
      individualMonitor = LINQ.first(this.individualMonitors,
        (monitor) => monitor.isMonitored(symbol));

      // check if there is an individual monitor with free capacity
      if (!individualMonitor) {
        individualMonitor = LINQ.first(this.individualMonitors,
          (monitor) =>
            LINQ.count(monitor.getMonitoredSymbols()) < this.configs.individualMonitors_maxSymbols);
      }
    }

    if (individualMonitor) {
      // We'll stop fetchMonitor after individual monitor connection is established.
      // In case there is no fetch monitor (no cases yet), but there is an individual monitor,
      // we'll start monitoring on it directly. User will have to wait longer to get the first update.
      const result = this.promoteToIndividualMonitor(this.fetchMonitor, individualMonitor, symbol);
      monitoringStarted ||= result;
    } else {
      this.notUpgradedSymbols.push(symbol);
    }

    return monitoringStarted;
  }


  public unsubscribe(item: MonitoredRequest): boolean {
    const monitoredRequests = this.symbolSubscribers.get(item.symbols);
    if (!monitoredRequests) {
      return false;
    }

    if (!monitoredRequests.delete(item)) {
      return false;
    }

    if (monitoredRequests.size === 0
      && !this.stopMonitoring(item.symbols)) {
      logger.error(`Failed to stop monitoring '${item.symbols}' on ${this.exchange.id}`);
    }

    // still have subscribers, don't stop monitoring
    return true;
  }

  private stopMonitoring(symbol: string): boolean {
    logger.info(`balancer stopMonitoring '${symbol}'`);

    if (!this.symbolSubscribers.delete(symbol)) {
      logger.error(`Failed to delete symbol '${symbol}' from symbolSubscribers on ${this.exchange.id}`);
      return false;
    }

    const index = this.notUpgradedSymbols.indexOf(symbol);
    if (index !== -1) {
      this.notUpgradedSymbols.splice(index, 1);
    }

    for (let i = this.upgradeTransitions.length - 1; i >= 0; i--) {
      if (this.upgradeTransitions[i].symbol === symbol) {
        this.upgradeTransitions[i].cancel();
        this.upgradeTransitions.splice(i, 1);
      }
    }

    for (let i = this.downgradeTransitions.length - 1; i >= 0; i--) {
      if (this.downgradeTransitions[i].symbol === symbol) {
        this.downgradeTransitions[i].cancel();
        this.downgradeTransitions.splice(i, 1);
      }
    }

    let foundMonitor = false;
    if (this.groupMonitors !== undefined) {
      const monitor = LINQ.first(this.groupMonitors,
        (monitor) => monitor.isSubscribed(symbol));
      if (monitor) {
        const result = monitor.unsubscribe(symbol);
        foundMonitor ||= result;
      }
    }

    if (this.individualMonitors !== undefined) {
      const monitor = LINQ.first(this.individualMonitors,
        (monitor) => monitor.isSubscribed(symbol));
      if (monitor) {
        const result = monitor.unsubscribe(symbol);
        foundMonitor ||= result;
      }
    }

    if (this.fetchMonitor?.isSubscribed(symbol)) {
      const result = this.fetchMonitor.unsubscribe(symbol);
      foundMonitor ||= result;
    }

    if (!foundMonitor) {
      logger.error(`No monitor found for '${symbol}' on ${this.exchange.id}`);
    }
    return foundMonitor;
  }

  // overriden, because this.symbolSubscribers doesn't represent the actual monitored symbols.
  // we unsubscribe instantly, while the monitor might still be in the process of monitoring symbol.
  public isMonitored(symbol: string): boolean {
    if (this.fetchMonitor?.isMonitored(symbol)) {
      return true;
    }

    if (this.individualMonitors !== undefined && LINQ.any(this.individualMonitors,
      (monitor) => monitor.isMonitored(symbol))) {
      return true;
    }

    if (this.groupMonitors !== undefined && LINQ.any(this.groupMonitors,
      (monitor) => monitor.isMonitored(symbol))) {
      return true;
    }

    return false;
  }

  public* getSubscribers(symbol?: string): IterableIterator<MonitoredRequest> {
    if (symbol === undefined) {
      for (const set of this.symbolSubscribers.values()) {
        for (const request of set) {
          yield request;
        }
      }
    } else {
      const set = this.symbolSubscribers.get(symbol);
      if (set) {
        for (const request of set) {
          yield request;
        }
      }
    }
  }

  public* getMonitoredSymbols(): IterableIterator<string> {
    if (this.groupMonitors !== undefined) {
      for (const monitor of this.groupMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }

    if (this.individualMonitors !== undefined) {
      for (const monitor of this.individualMonitors) {
        for (const symbol of monitor.getMonitoredSymbols()) {
          yield symbol;
        }
      }
    }

    if (this.fetchMonitor) {
      for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
        yield symbol;
      }
    }
  }

  private async rebalanceLoop(): Promise<void> {
    while (true) {
      await new Promise(resolve => setTimeout(resolve, this.configs.rebalance_interval));

      if (this.notUpgradedSymbols.length === 0
        && (this.groupMonitors === undefined || this.groupMonitors.length === 0)) {
        continue;
      }

      logger.info(`Rebalancing ${this.exchange.id}`);
      try {
        await this.rebalanceUp();
        await this.rebalanceDown();
      } catch (error) {
        logger.error(error, 'BalancerOrderbookMonitor rebalanceLoop error:');
      }
    }
  }

  private async rebalanceUp(): Promise<void> {
    //   await this.rebalanceUpToGroups();
    await this.rebalanceUpToIndividual();
  }

  // private async rebalanceUpToGroups() {
  //   if (this.groupMonitors === undefined) {
  //     return;
  //   }
  //
  //   // first, try to upgrade to the existing group monitors
  //   let symbolsToPromote: string[] = [];
  //   for (const monitor of this.groupMonitors) {
  //     const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
  //       (symbol) => monitor.staticSymbols.includes(symbol) && monitor.hasReceivedFirstUpdate(symbol));
  //
  //     symbolsToPromote.length = 0;
  //     LINQ.toArray(iterator, symbolsToPromote);
  //
  //     if (!LINQ.any(monitor.getMonitoredSymbols()) 
  //       && symbolsToPromote.length <= this.configs.minSymbolsToStartGroupMonitor) {
  //       // not enough symbols to justify starting monitoring entire group
  //       continue;
  //     }
  //
  //     this.promoteToGroupMonitor(symbolsToPromote, monitor);
  //   }
  //
  //   // exclude symbols that can be upgraded to the existing group monitors later
  //   const iterator = LINQ.where(this.fetchAndIndividualMonitorSymbols(),
  //     (symbol) => LINQ.all(this.groupMonitors,
  //       (monitor) => !monitor.staticSymbols.includes(symbol)));
  //   const notUpgradedSymbols = LINQ.toArray(iterator);
  //
  //   // const symbolAvgEmaRequestPeriod =
  //   //   this.getSymbolsAvgEmaRequestPeriod(notUpgradedSymbols);
  //   //
  //   // // order symbols by emaRequestPeriod
  //   // notUpgradedSymbols.sort((a, b) => {
  //   //   const aEma = symbolAvgEmaRequestPeriod.get(a);
  //   //   const bEma = symbolAvgEmaRequestPeriod.get(b);
  //   //   return aEma - bEma;
  //   // });
  //
  //   // create new group monitors and upgrade remaining symbols to them
  //   if (notUpgradedSymbols.length > this.configs.minSymbolsToCreateNewGroup) {
  //     const groups = this.sliceSymbolsIntoGroups(
  //       notUpgradedSymbols, this.configs.minSymbolsToCreateNewGroup, this.configs.maxSymbolsPerGroupMonitor);
  //     for (const group of groups) {
  //       const newMonitor = await this.addNewGroupMonitor(group);
  //       newMonitor.onMonitoringFinish = () => this.removeGroupMonitor(newMonitor);
  //       logger.info(`Created new groupMonitor '${newMonitor.id}' for ${group.length} symbols`);
  //       // we'll promote these symbols later, when we get first update
  //       // this.promoteToGroupMonitor(group, newMonitor);
  //     }
  //   }
  // }
  //
  // private* fetchAndIndividualMonitorSymbols(): IterableIterator<string> {
  //   if (this.fetchMonitor !== undefined) {
  //     for (const symbol of this.fetchMonitor.getMonitoredSymbols()) {
  //       yield symbol;
  //     }
  //   }
  //
  //   if (this.individualMonitors !== undefined) {
  //     for (const monitor of this.individualMonitors) {
  //       for (const symbol of monitor.getMonitoredSymbols()) {
  //         yield symbol;
  //       }
  //     }
  //   }
  // }
  //
  // private promoteToGroupMonitor(symbols: string[], monitor: WatchGroupOrderbookMonitor) {
  //   for (const symbol of symbols) {
  //     if (this.fetchMonitor && this.fetchMonitor.isSubscribed(symbol)) {
  //       logger.info(`[${symbol}] MovesUP fetchWatcher -> groupMonitor '${monitor.id}'`);
  //       this.subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
  //       LINQ.toArray(this.fetchMonitor.getSubscribers(symbol), this.subscribers);
  //       for (const subscriber of this.subscribers) {
  //         this.fetchMonitor.unsubscribe(subscriber);
  //         monitor.subscribe(subscriber);
  //       }
  //     }
  //
  //     if (this.individualMonitors !== undefined) {
  //       const individualMonitor = LINQ.first(this.individualMonitors,
  //         (monitor) => monitor.isMonitored(symbol));
  //       if (!individualMonitor || !individualMonitor.isSubscribed(symbol)) {
  //         continue;
  //       }
  //       logger.info(`[${symbol}] MovesUP individualMonitor '${individualMonitor.id}' -> groupMonitor '${monitor.id}'`);
  //       this.subscribers.length = 0; // copy list, because we'll alter subscribers in the loop
  //       LINQ.toArray(individualMonitor.getSubscribers(symbol), this.subscribers);
  //       for (const subscriber of this.subscribers) {
  //         individualMonitor.unsubscribe(subscriber);
  //         monitor.subscribe(subscriber);
  //       }
  //     }
  //   }
  // }

  private async rebalanceUpToIndividual(): Promise<void> {
    if (this.configs.individualMonitors_count <= 1) {
      return;
    }

    if (this.notUpgradedSymbols.length === 0) {
      return;
    }

    for (const individualMonitor of this.individualMonitors) {
      const monitoredSymbolsCount = LINQ.count(individualMonitor.getMonitoredSymbols());
      let remainingCapacity = this.configs.individualMonitors_maxSymbols - monitoredSymbolsCount;
      while (this.notUpgradedSymbols.length > 0 && remainingCapacity > 0) {
        const symbol = this.notUpgradedSymbols.pop();
        this.promoteToIndividualMonitor(this.fetchMonitor, individualMonitor, symbol);
        remainingCapacity--;
      }
    }

    if (this.notUpgradedSymbols.length === 0) {
      return;
    }

    const groups = this.sliceSymbolsIntoGroups(
      this.notUpgradedSymbols, 1, this.configs.individualMonitors_maxSymbols);
    for (const group of groups) {
      const newMonitor = await this.addNewIndividualMonitor();

      // ensure all symbols are still needed
      for (let i = group.length - 1; i >= 0; i--) {
        if (!this.notUpgradedSymbols.includes(group[i])) {
          group.splice(i, 1);
        }
        group.sort();
      }

      if (group.length === 0) {
        logger.warn(`while creating new individualMonitor '${newMonitor.id}', it got not needed, so removing it.`);
        this.removeIndividualMonitor(newMonitor);
        continue;
      }

      newMonitor.onMonitoringFinish = () => this.removeIndividualMonitor(newMonitor);
      logger.info(`Created new individualMonitor '${newMonitor.id}' for ${group.length} symbols: ${group.join(', ')}`);
      for (let i = 0; i < group.length; i++) {
        //logger.info(`${i+1}/${group.length}: promoteToIndividualMonitor '${group[i]}' fetchWatcher -> individualMonitor '${newMonitor.id}'`);
        this.promoteToIndividualMonitor(this.fetchMonitor, newMonitor, group[i]);
      }
    }
  }

  private promoteToIndividualMonitor(
    fetchMonitor: FetchIndividualOrderbookMonitor, individualMonitor: WatchIndividualExchangeMonitor, symbol: string): boolean {
    const index = this.notUpgradedSymbols.indexOf(symbol);
    if (index !== -1) {
      // logger.info(`remove [${symbol}] from notUpgradedSymbols`);
      this.notUpgradedSymbols.splice(index, 1);
    }

    if (fetchMonitor && fetchMonitor.isSubscribed(symbol)) {
      const transition = new RebalanceTransition(symbol, fetchMonitor, individualMonitor);
      this.upgradeTransitions.push(transition);
      transition.upgradePromise.finally(() => {
        const index = this.upgradeTransitions.indexOf(transition);
        if (index !== -1) {
          this.upgradeTransitions.splice(index, 1);
        }
      });
      return false;
    } else {
      logger.info(`[${symbol}] MovesUP NOT_Watched -> individualMonitor '${individualMonitor.id}'`);
      if(!individualMonitor.isSubscribed(symbol)){
        individualMonitor.subscribe(symbol);
      }
      return true;
    }
  }

  private sliceSymbolsIntoGroups(symbols: string[], minSymbolsPerGroup: number, maxSymbolsPerGroup?: number): string[][] {
    if (maxSymbolsPerGroup && minSymbolsPerGroup > maxSymbolsPerGroup) {
      throw new Error(`minSymbolsPerGroup ${minSymbolsPerGroup} is greater than maxSymbolsPerGroup ${maxSymbolsPerGroup}`);
    }

    const total = symbols.length;

    // Guard: not enough symbols for one group
    if (total < minSymbolsPerGroup) {
      return [];
    }

    // Guard: not enough symbols for two groups, but enough for one
    if (maxSymbolsPerGroup && total < maxSymbolsPerGroup) {
      return [LINQ.toArray(symbols)];
    }

    const numGroups = maxSymbolsPerGroup ? Math.floor(total / maxSymbolsPerGroup) : 1;

    // Start with a base group size
    let groupSize = Math.floor(total / numGroups);
    if (groupSize < minSymbolsPerGroup) {
      groupSize = minSymbolsPerGroup;
    }
    if (maxSymbolsPerGroup && groupSize > maxSymbolsPerGroup) {
      groupSize = maxSymbolsPerGroup;
    }

    const result: string[][] = [];
    let index = 0;
    for (let i = 0; i < numGroups; i++) {
      result.push(symbols.slice(index, index + groupSize));
      index += groupSize;
    }

    // If undistributed symbols remain here, they are ignored (not assigned to any group)
    return result;
  }

  private removeIndividualMonitor(monitor: WatchIndividualExchangeMonitor) {
    if (LINQ.any(monitor.getMonitoredSymbols())) {
      throw new Error(`You cannot remove this individual monitor (${monitor.id}). It's still running.`);
    }

    logger.info(`Removing not running individual monitor (${monitor.id})`);
    const index = this.individualMonitors.indexOf(monitor);
    if (index !== -1) {
      this.individualMonitors.splice(index, 1);
    }

    monitor.destroy().catch((error) => logger.error(error, `Failed to destroy individual monitor ${monitor.id}`));
  }

  private removeGroupMonitor(monitor: WatchGroupOrderbookMonitor) {
    if (LINQ.any(monitor.getMonitoredSymbols())) {
      throw new Error(`You cannot remove this group monitor (${monitor.id}). It's still running.`);
    }

    logger.info(`Removing not running group monitor (${monitor.id})`);
    const index = this.groupMonitors.indexOf(monitor);
    if (index !== -1) {
      this.groupMonitors.splice(index, 1);
    }

    monitor.destroy().catch((error) => logger.error(error, `Failed to destroy group monitor ${monitor.id}`));
  }

  private getSymbolsAvgEmaRequestPeriod(symbols: string[]): Map<string, number> {
    const result = new Map<string, number>();
    for (const symbol of symbols
      ) {
      let total = 0;
      let count = 0;
      for (const subscriber of this.getSubscribers(symbol)) {
        total += subscriber.emaRequestPeriod;
        count++;
      }
      result.set(symbol, this.getAvgEmaRequestPeriod(symbol));
    }
    return result;
  }

  private getAvgEmaRequestPeriod(symbol: string): number {
    let total = 0;
    let count = 0;
    for (const subscriber of this.getSubscribers(symbol)) {
      total += subscriber.emaRequestPeriod;
      count++;
    }

    return Math.round(total / count);
  }

  private async rebalanceDown(): Promise<void> {
    if (!this.groupMonitors) {
      return;
    }

    if (!this.individualMonitors && !this.fetchMonitor) {
      return;
    }

    for (const monitor of this.groupMonitors) {
      if (!LINQ.any(monitor.getMonitoredSymbols())) {
        continue;
      }

      let subscribedSymbolsCount = 0;
      for (const symbol of monitor.getMonitoredSymbols()) {
        if (monitor.isSubscribed(symbol)) {
          subscribedSymbolsCount++;
        }
      }

      if (subscribedSymbolsCount > this.configs.rebalance_minSymbolsToStartGroup) {
        // there are still enough symbols to justify a multiplexed connection
        continue;
      }

      for (const symbol of monitor.getMonitoredSymbols()) {
        if (!this.symbolSubscribers.get(symbol)) { // no subscribers or empty
          continue;
        }

        if (this.individualMonitors !== undefined) {
          const individualMonitor = LINQ.min(this.individualMonitors,
            (monitor) => LINQ.count(monitor.getMonitoredSymbols()),
            (monitor) => monitor);

          let connectionsLeft =
            this.configs.individualMonitors_maxSymbols - (LINQ.count(individualMonitor.getMonitoredSymbols()));
          if (connectionsLeft > 0) {
            this.downgradeToIndividualMonitor(monitor, individualMonitor, symbol);
            continue;
          }
        }

        if (this.fetchMonitor) {
          this.downgradeToFetchMonitor(monitor, this.fetchMonitor, symbol);
        }
      }
    }
  }

  private downgradeToIndividualMonitor(
    groupMonitor: WatchGroupOrderbookMonitor, individualMonitor: WatchIndividualExchangeMonitor, symbol: string) {
    if (groupMonitor && groupMonitor.isSubscribed(symbol)) {
      const transition = new RebalanceTransition(symbol, groupMonitor, individualMonitor);
      this.upgradeTransitions.push(transition);
      transition.upgradePromise.finally(() => {
        const index = this.upgradeTransitions.indexOf(transition);
        if (index !== -1) {
          this.upgradeTransitions.splice(index, 1);
        }
      });
    } else {
      logger.info(`[${symbol}] MovesDOWN NOT_Watched -> individualMonitor '${individualMonitor.id}'`);
      individualMonitor.subscribe(symbol);
    }
  }

  private downgradeToFetchMonitor(
    groupMonitor: WatchGroupOrderbookMonitor, fetchMonitor: FetchIndividualOrderbookMonitor, symbol: string) {
    if (groupMonitor && groupMonitor.isSubscribed(symbol)) {
      const transition = new RebalanceTransition(symbol, groupMonitor, fetchMonitor);
      this.upgradeTransitions.push(transition);
      transition.upgradePromise.finally(() => {
        const index = this.upgradeTransitions.indexOf(transition);
        if (index !== -1) {
          this.upgradeTransitions.splice(index, 1);
        }
      });
    } else {
      logger.info(`[${symbol}] MovesDOWN NOT_Watched -> fetchWatcher`);
      fetchMonitor.subscribe(symbol);
    }
  }


  public async destroy(): Promise<void> {
    const promises: Promise<void>[] = [];

    if (this.fetchMonitor !== undefined) {
      promises.push(this.fetchMonitor.destroy());
    }

    if (this.individualMonitors !== undefined) {
      for (const monitor of this.individualMonitors) {
        promises.push(monitor.destroy());
      }
    }

    if (this.groupMonitors !== undefined) {
      for (const monitor of this.groupMonitors) {
        promises.push(monitor.destroy());
      }
    }

    await Promise.all(promises);
  }

  public toString(): string {
    let notUpgraded = 'notUpgradedSymbols (none)';
    if (this.notUpgradedSymbols.length > 0) {
      notUpgraded = `notUpgradedSymbols (${this.notUpgradedSymbols.length}): ${this.notUpgradedSymbols.sort().join(', ')}`;
    }

    let fetch = 'fetchWatcher (none)';
    if (this.fetchMonitor !== undefined) {
      const symbols = LINQ.toArray(this.fetchMonitor.getMonitoredSymbols());
      fetch = `fetchWatcher (${symbols.length}): ${symbols.sort().join(', ')}`;
      // fetch = `fetchWatcher (${symbols.length})}`;
    }

    let individual = 'individualMonitors (none)';
    if (this.individualMonitors !== undefined) {
      const runningMonitors = LINQ.count(this.individualMonitors,
        (monitor) => LINQ.any(monitor.getMonitoredSymbols()));
      if (runningMonitors === 0) {
        individual = `0/${this.individualMonitors.length} individualMonitors (none)`;
      } else {
        individual = `${runningMonitors}/${this.individualMonitors.length} individualMonitors: `;

        for (const monitor of this.individualMonitors) {
          if (LINQ.any(monitor.getMonitoredSymbols())) {
            const symbols = LINQ.toArray(monitor.getMonitoredSymbols()).sort();
            individual += `${os.EOL} \t '${monitor.id}': (${symbols.length}): ${symbols.join(', ')}`;
            // const symbols = LINQ.count(monitor.getMonitoredSymbols());
            // individual += ` '${monitor.id}':(${symbols})`;
          }
        }
      }
    }

    let groupStats = 'groupMonitors (none)';
    if (this.groupMonitors !== undefined) {
      const runningMonitors = LINQ.count(this.groupMonitors,
        (monitor) => LINQ.any(monitor.getMonitoredSymbols()));
      if (runningMonitors === 0) {
        groupStats = `0/${this.groupMonitors.length} groupMonitors (none)`;
      } else {
        groupStats = `${runningMonitors}/${this.groupMonitors.length} groupMonitors: `;

        for (const monitor of this.groupMonitors) {
          if (LINQ.any(monitor.getMonitoredSymbols())) {
            const symbols = LINQ.toArray(monitor.getMonitoredSymbols()).sort();
            groupStats += `${os.EOL} \t '${monitor.id}': (${symbols.length}): ${symbols.join(', ')}`;
            // const symbols = LINQ.count(monitor.getMonitoredSymbols());
            // groupStats += ` '${monitor.id}':(${symbols})`;
          }
        }
      }
    }

    let upgradeTransitions = 'upgradeTransitions (none)';
    if (this.upgradeTransitions.length > 0) {
      const symbols = this.upgradeTransitions.map(t => t.symbol).sort();
      upgradeTransitions = `upgradeTransitions (${symbols.length}): ${symbols.join(', ')}`;
    }

    // let downgradeTransitions = 'downgradeTransitions (none)';
    // if(this.downgradeTransitions.length > 0){
    //   const symbols = this.downgradeTransitions.map(t => t.symbol).sort();
    //   downgradeTransitions = `downgradeTransitions (${symbols.length}): ${symbols.join(', ')}`;
    // }

    return `
      ${notUpgraded}
      ${fetch}
      ${individual}
      ${groupStats}
      ${upgradeTransitions}`;
  }
}
