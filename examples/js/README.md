# CCXT JavaScript Examples

These examples might require the following super-useful high-quality Node.js modules by [xpl](https://github.com/xpl):

- [ansicolor](https://github.com/xpl/ansicolor): A quality JavaScript library for the ANSI color/style management ([ansicolor @ npm](https://npmjs.com/package/ansicolor))
- [as-table](https://github.com/xpl/as-table): A simple function that prints objects as ASCII tables ([as-table @ npm](https://npmjs.com/package/as-table))
- [ololog](https://github.com/xpl/ololog): Platform-agnostic logging with blackjack and hookers ([ololog @ npm](https://npmjs.com/package/ololog))

All of the modules above are installed with the ccxt library devDependencies by npm automatically.

To run the ccxt JavaScript examples from any folder type in console:

```shell
node path/to/example.js # substitute for actual filename here
```
