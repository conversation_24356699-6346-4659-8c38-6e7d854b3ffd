this is a node js, typescript project that is aimed to aggregate crypto data from different exchanges. 
it uses ccxt library for that. and there is the npm version of this library in './node_modules' folder and the source code of this library in './ccxt-source' folder (this one is git-ignored, because it's not involved in the project and used only to investigate the library related issues).

i test my code with 'htx' exchange and get these errors:
<logs>
(node:2967) TimeoutNegativeWarning: -2 is a negative number.
Timeout duration was set to 1.
    at Timeout (node:internal/timers:195:17)
    at setTimeout (node:timers:163:19)
    at htx.delay (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/Exchange.js:920:9)
    at htx.handleOrderBookSnapshot (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:427:30)
    at htx.handleSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:1717:24)
    at htx.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:2049:22)
    at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
    at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
[06:57:17.362] ERROR: WatchIndividualExchangeMonitor 1 'watchSymbol' STRK/USDT error: 
    err: {
      "type": "InvalidNonce",
      "message": "htx failed to synchronize WebSocket feed with the snapshot for symbol STRK/USDT in 3 attempts",
      "stack":
          InvalidNonce: htx failed to synchronize WebSocket feed with the snapshot for symbol STRK/USDT in 3 attempts
              at htx.handleOrderBookSnapshot (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:432:27)
              at htx.handleSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:1717:24)
              at htx.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:2049:22)
              at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
              at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
              at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
              at WebSocket.emit (node:events:507:28)
              at Receiver.receiverOnMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1220:20)
              at Receiver.emit (node:events:507:28)
              at Receiver.dataMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/receiver.js:569:14)
      "name": "InvalidNonce"
    }
</logs>

the first error is happening not very often but it's quite unclear what's going on there. it can happen right at the app start or after a while. both errors happen with a single individual monitor and a multiple instances (regardless if they share an exchange instance or no). I tried to increase the start watch delay to make sure there are not many symbols trying to start watching at once, but it didn't help. it's not related to unwatch logic either, because this error appears even when there are only new symbols added, but no unwatch calls.

could you please investigate my code and the source code of ccxt library (in './ccxt-source' folder) and tell me why this error is happening? give me your advice on how to fix it, but do not change any code yet.


investigate code of ccxt library placed in './ccxt-source' folder (this one is git-ignored, because it's not involved in the projectand used only to investigate the library related issues). rely on 'Exchange' class, because i know it's related to this topic the most and explain to me what values can 'exchange.options' parameter have? which of them are related to 'exchange.watchOrderBook'/'exchange.watchOrderBookForSymbols'?

 investigate code of ccxt library placed in './ccxt-source' folder (this one is git-ignored, because it's not involved in the project and used only to investigate the library related issues). rely on 'Exchange' class, cause i know it's related to this topic the most and explain to me how rate limiting is implemented there? if i use 'exchange.watchOrderBook' to watch a lot of symbols and get 'InvalidNonce' error - is it related? how can i controll how many request can be sent at each moment? how can i know how many requests is allowed to sent at each moment? i'm interested in how this all can be applied to 'htx' exchange implementation.  