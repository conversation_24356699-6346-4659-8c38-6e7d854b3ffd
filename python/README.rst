CCXT – CryptoCurrency eXchange Trading Library
==============================================

`Build Status <https://travis-ci.org/ccxt/ccxt>`__ `npm <https://npmjs.com/package/ccxt>`__ `PyPI <https://pypi.python.org/pypi/ccxt>`__ `NPM Downloads <https://www.npmjs.com/package/ccxt>`__ `Discord <https://discord.gg/dhzSKYU>`__ `Supported Exchanges <https://github.com/ccxt/ccxt/wiki/Exchange-Markets>`__ `Open Collective <https://opencollective.com/ccxt>`__
`Twitter Follow <https://twitter.com/ccxt_official>`__

A JavaScript / Python / PHP library for cryptocurrency trading and e-commerce with support for many bitcoin/ether/altcoin exchange markets and merchant APIs.

Install · Usage · `Manual <https://github.com/ccxt/ccxt/wiki>`__ · `FAQ <https://github.com/ccxt/ccxt/wiki/FAQ>`__ · `Examples <https://github.com/ccxt/ccxt/tree/master/examples>`__ · `Contributing <https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md>`__ · Social
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The **CCXT** library is used to connect and trade with cryptocurrency exchanges and payment processing services worldwide. It provides quick access to market data for storage, analysis, visualization, indicator development, algorithmic trading, strategy backtesting, bot programming, and related software engineering.

It is intended to be used by **coders, developers, technically-skilled traders, data-scientists and financial analysts** for building trading algorithms.

Current feature list:

-  support for many cryptocurrency exchanges — more coming soon
-  fully implemented public and private APIs
-  optional normalized data for cross-exchange analytics and arbitrage
-  an out of the box unified API that is extremely easy to integrate
-  works in Node 7.6+, Python 3, PHP 5.4+, and web browsers

Sponsored Promotion
-------------------

+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| `Bitvavo – Trade The Future <https://bitvavo.com>`__                                                                                                                                                                                             |
+==================================================================================================================================================================================================================================================+
| `CCXT Pro – A JavaScript / Python / PHP cryptocurrency exchange trading WebSocket API for professionals <https://ccxt.pro>`__ `A JavaScript / Python / PHP cryptocurrency exchange trading WebSocket API for professionals <https://ccxt.pro>`__ |
+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

See Also
--------

-  \ `Quadency <https://quadency.com?utm_source=ccxt>`__\   `Quadency <https://quadency.com?utm_source=ccxt>`__ — professional crypto terminal, algo trading, and unified streaming APIs.
-  \ `TabTrader <https://tab-trader.com/?utm_source=ccxt>`__\   `TabTrader <https://tab-trader.com/?utm_source=ccxt>`__ — trading on all exchanges in one app. Avaliable on `Android <https://play.google.com/store/apps/details?id=com.tabtrader.android&referrer=utm_source%3Dccxt>`__ and `iOS <https://itunes.apple.com/app/apple-store/id1095716562?mt=8>`__.
-  \ `Currency.com <https://currency.com/?utm_source=ccxt>`__\   `Currency.com <https://currency.com/?utm_source=ccxt>`__ — Award-winning regulated tokenized assets platform with 1500+ available tokens and cryptos.

Certified Cryptocurrency Exchanges
----------------------------------

+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
|        logo                                                                 | id        | name                                                                        | ver | doc                                                                                   | certified                                                            | pro                             |
+=============================================================================+===========+=============================================================================+=====+=======================================================================================+======================================================================+=================================+
| `binance <https://www.binance.com/?ref=10205187>`__                         | binance   | `Binance <https://www.binance.com/?ref=10205187>`__                         | \*  | `API <https://binance-docs.github.io/apidocs/spot/en>`__                              | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bitfinex <https://www.bitfinex.com/?refcode=P61eYxFL>`__                   | bitfinex  | `Bitfinex <https://www.bitfinex.com/?refcode=P61eYxFL>`__                   | 1   | `API <https://docs.bitfinex.com/v1/docs>`__                                           | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bittrex <https://bittrex.com/Account/Register?referralCode=1ZE-G0G-M3B>`__ | bittrex   | `Bittrex <https://bittrex.com/Account/Register?referralCode=1ZE-G0G-M3B>`__ | 1.1 | `API <https://bittrex.github.io/api/>`__                                              | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bitvavo <https://bitvavo.com/?a=24F34952F7>`__                             | bitvavo   | `Bitvavo <https://bitvavo.com/?a=24F34952F7>`__                             | 2   | `API <https://docs.bitvavo.com/>`__                                                   | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bytetrade <https://www.byte-trade.com>`__                                  | bytetrade | `ByteTrade <https://www.byte-trade.com>`__                                  | \*  | `API <https://github.com/Bytetrade/bytetrade-official-api-docs/wiki>`__               | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `eterbase <https://www.eterbase.com>`__                                     | eterbase  | `Eterbase <https://www.eterbase.com>`__                                     | 1   | `API <https://developers.eterbase.exchange>`__                                        | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ftx <https://ftx.com/#a=1623029>`__                                        | ftx       | `FTX <https://ftx.com/#a=1623029>`__                                        | \*  | `API <https://github.com/ftexchange/ftx>`__                                           | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `idex <https://idex.market>`__                                              | idex      | `IDEX <https://idex.market>`__                                              | \*  | `API <https://docs.idex.market/>`__                                                   | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `kraken <https://www.kraken.com>`__                                         | kraken    | `Kraken <https://www.kraken.com>`__                                         | 0   | `API <https://www.kraken.com/features/api>`__                                         | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `poloniex <https://poloniex.com/signup?c=UBFZJRPJ>`__                       | poloniex  | `Poloniex <https://poloniex.com/signup?c=UBFZJRPJ>`__                       | \*  | `API <https://docs.poloniex.com>`__                                                   | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `upbit <https://upbit.com>`__                                               | upbit     | `Upbit <https://upbit.com>`__                                               | 1   | `API <https://docs.upbit.com/docs/%EC%9A%94%EC%B2%AD-%EC%88%98-%EC%A0%9C%ED%95%9C>`__ | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------+-----------+-----------------------------------------------------------------------------+-----+---------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+

Supported Cryptocurrency Exchange Markets
-----------------------------------------

The CCXT library currently supports the following 124 cryptocurrency exchange markets and trading APIs:

+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| name                                                                                    | ver | doc                                                                                             | certified                                                            | pro                             |
+=========================================================================================+=====+=================================================================================================+======================================================================+=================================+
| `1BTCXE <https://1btcxe.com>`__                                                         | \*  | `API <https://1btcxe.com/api-docs.php>`__                                                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ACX <https://acx.io>`__                                                                | 2   | `API <https://acx.io/documents/api_v2>`__                                                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ANXPro <https://anxpro.com>`__                                                         | \*  | `API <https://anxv2.docs.apiary.io>`__                                                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `AOFEX <https://aofex.com/#/register?key=9763840>`__                                    | \*  | `API <https://aofex.zendesk.com/hc/en-us/sections/360005576574-API>`__                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BCEX <https://www.bcex.top/register?invite_code=758978&lang=en>`__                     | 1   | `API <https://github.com/BCEX-TECHNOLOGY-LIMITED/API_Docs/wiki/Interface>`__                    |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bequant <https://bequant.io>`__                                                        | 2   | `API <https://api.bequant.io/>`__                                                               |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bibox <https://w2.bibox.com/login/register?invite_code=05Kj3I>`__                      | 1   | `API <https://biboxcom.github.io/en/>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BigONE <https://b1.run/users/new?code=D3LLBVFT>`__                                     | 3   | `API <https://open.big.one/docs/api.html>`__                                                    |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Binance <https://www.binance.com/?ref=10205187>`__                                     | \*  | `API <https://binance-docs.github.io/apidocs/spot/en>`__                                        | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Binance Jersey <https://www.binance.je/?ref=35047921>`__                               | \*  | `API <https://github.com/binance-exchange/binance-official-api-docs/blob/master/rest-api.md>`__ |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Binance US <https://www.binance.us/?ref=35005074>`__                                   | \*  | `API <https://github.com/binance-us/binance-official-api-docs>`__                               |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bit2C <https://bit2c.co.il/Aff/63bfed10-e359-420c-ab5a-ad368dab0baf>`__                | \*  | `API <https://www.bit2c.co.il/home/<USER>
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bitbank <https://bitbank.cc/>`__                                                       | 1   | `API <https://docs.bitbank.cc/>`__                                                              |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BitBay <https://auth.bitbay.net/ref/jHlbB4mIkdS1>`__                                   | \*  | `API <https://bitbay.net/public-api>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitfinex <https://www.bitfinex.com/?refcode=P61eYxFL>`__                               | 1   | `API <https://docs.bitfinex.com/v1/docs>`__                                                     | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitfinex <https://www.bitfinex.com/?refcode=P61eYxFL>`__                               | 2   | `API <https://docs.bitfinex.com/v2/docs/>`__                                                    |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bitFlyer <https://bitflyer.jp>`__                                                      | 1   | `API <https://lightning.bitflyer.com/docs?lang=en>`__                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitforex <https://www.bitforex.com/en/invitationRegister?inviterId=1867438>`__         | 1   | `API <https://github.com/githubdev2020/API_Doc_en/wiki>`__                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bithumb <https://www.bithumb.com>`__                                                   | \*  | `API <https://apidocs.bithumb.com>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `bitkk <https://www.bitkk.com>`__                                                       | 1   | `API <https://www.bitkk.com/i/developer>`__                                                     |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BitMart <http://www.bitmart.com/?r=rQCFLh>`__                                          | 2   | `API <https://github.com/bitmartexchange/bitmart-official-api-docs>`__                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BitMax <https://bitmax.io/#/register?inviteCode=EL6BXBQM>`__                           | 1   | `API <https://bitmax-exchange.github.io/bitmax-pro-api/#bitmax-pro-api-documentation>`__        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BitMEX <https://www.bitmex.com/register/upZpOX>`__                                     | 1   | `API <https://www.bitmex.com/app/apiOverview>`__                                                |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitso <https://bitso.com/?ref=itej>`__                                                 | 3   | `API <https://bitso.com/api_info>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitstamp <https://www.bitstamp.net>`__                                                 | 2   | `API <https://www.bitstamp.net/api>`__                                                          |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitstamp <https://www.bitstamp.net>`__                                                 | 1   | `API <https://www.bitstamp.net/api>`__                                                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bittrex <https://bittrex.com/Account/Register?referralCode=1ZE-G0G-M3B>`__             | 1.1 | `API <https://bittrex.github.io/api/>`__                                                        | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bitvavo <https://bitvavo.com/?a=24F34952F7>`__                                         | 2   | `API <https://docs.bitvavo.com/>`__                                                             | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bit-Z <https://u.bitz.com/register?invite_code=1429193>`__                             | 2   | `API <https://apidoc.bitz.com/en/>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BL3P <https://bl3p.eu>`__                                                              | 1   | `API <https://github.com/BitonicNL/bl3p-api/tree/master/docs>`__                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bleutrade <https://bleutrade.com>`__                                                   | \*  | `API <https://app.swaggerhub.com/apis-docs/bleu/white-label/3.0.0>`__                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Braziliex <https://braziliex.com/?ref=5FE61AB6F6D67DA885BC98BA27223465>`__             | \*  | `API <https://braziliex.com/exchange/api.php>`__                                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BTC-Alpha <https://btc-alpha.com/?r=123788>`__                                         | 1   | `API <https://btc-alpha.github.io/api-docs>`__                                                  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BtcBox <https://www.btcbox.co.jp/>`__                                                  | 1   | `API <https://www.btcbox.co.jp/help/asm>`__                                                     |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BTC Markets <https://btcmarkets.net>`__                                                | \*  | `API <https://github.com/BTCMarkets/API>`__                                                     |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BtcTrade.im <https://m.baobi.com/invite?inv=1765b2>`__                                 | \*  | `API <https://www.btctrade.im/help.api.html>`__                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BTC Trade UA <https://btc-trade.com.ua/registration/22689>`__                          | \*  | `API <https://docs.google.com/document/d/1ocYA0yMy_RXd561sfG3qEPZ80kyll36HUxvCRe5GbhE/edit>`__  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BTCTurk <https://www.btcturk.com>`__                                                   | \*  | `API <https://github.com/BTCTrader/broker-api-docs>`__                                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Buda <https://www.buda.com>`__                                                         | 2   | `API <https://api.buda.com>`__                                                                  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `BW <https://www.bw.com/regGetCommission/N3JuT1R3bWxKTE0>`__                            | 1   | `API <https://github.com/bw-exchange/api_docs_en/wiki>`__                                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Bybit <https://www.bybit.com/app/register?ref=X7Prm>`__                                | 2   | `API <https://bybit-exchange.github.io/docs/inverse/>`__                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ByteTrade <https://www.byte-trade.com>`__                                              | \*  | `API <https://github.com/Bytetrade/bytetrade-official-api-docs/wiki>`__                         | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CEX.IO <https://cex.io/r/0/up105393824/0/>`__                                          | \*  | `API <https://cex.io/cex-api>`__                                                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ChileBit <https://chilebit.net>`__                                                     | 1   | `API <https://blinktrade.com/docs>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Coinbase <https://www.coinbase.com/join/58cbe25a355148797479dbd2>`__                   | 2   | `API <https://developers.coinbase.com/api/v2>`__                                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Coinbase Prime <https://prime.coinbase.com>`__                                         | \*  | `API <https://docs.prime.coinbase.com>`__                                                       |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Coinbase Pro <https://pro.coinbase.com/>`__                                            | \*  | `API <https://docs.pro.coinbase.com>`__                                                         |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `coincheck <https://coincheck.com>`__                                                   | \*  | `API <https://coincheck.com/documents/exchange/api>`__                                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinEgg <https://www.coinegg.com/user/register?invite=523218>`__                       | \*  | `API <https://www.coinegg.com/explain.api.html>`__                                              |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinEx <https://www.coinex.com/register?refer_code=yw5fz>`__                           | 1   | `API <https://github.com/coinexcom/coinex_exchange_api/wiki>`__                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinFalcon <https://coinfalcon.com/?ref=CFJSVGTUPASB>`__                               | 1   | `API <https://docs.coinfalcon.com>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `coinfloor <https://www.coinfloor.co.uk>`__                                             | \*  | `API <https://github.com/coinfloor/api>`__                                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Coingi <https://www.coingi.com/?r=XTPPMC>`__                                           | \*  | `API <https://coingi.docs.apiary.io>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinMarketCap <https://coinmarketcap.com>`__                                           | 1   | `API <https://coinmarketcap.com/api>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinMate <https://coinmate.io?referral=YTFkM1RsOWFObVpmY1ZjMGREQmpTRnBsWjJJNVp3PT0>`__ | \*  | `API <https://coinmate.docs.apiary.io>`__                                                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinOne <https://coinone.co.kr>`__                                                     | 2   | `API <https://doc.coinone.co.kr>`__                                                             |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoinSpot <https://www.coinspot.com.au/register?code=PJURCU>`__                         | \*  | `API <https://www.coinspot.com.au/api>`__                                                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CoolCoin <https://www.coolcoin.com/user/register?invite_code=bhaega>`__                | \*  | `API <https://www.coolcoin.com/help.api.html>`__                                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `COSS <https://www.coss.io/c/reg?r=OWCMHQVW2Q>`__                                       | 1   | `API <https://api.coss.io/v1/spec>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `CREX24 <https://crex24.com/?refid=slxsjsjtil8xexl9hksr>`__                             | 2   | `API <https://docs.crex24.com/trade-api/v2>`__                                                  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Currency.com <https://currency.com/trading/signup?c=362jaimv&pid=referral>`__          | 1   | `API <https://currency.com/api>`__                                                              |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Deribit <https://www.deribit.com/reg-1189.4038>`__                                     | 2   | `API <https://docs.deribit.com/v2>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `DigiFinex <https://www.digifinex.vip/en-ww/from/DhOzBg/3798****5114>`__                | 3   | `API <https://docs.digifinex.vip>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `DSX <https://dsxglobal.com>`__                                                         | 3   | `API <https://dsxglobal.com/developers/publicApi>`__                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Eterbase <https://www.eterbase.com>`__                                                 | 1   | `API <https://developers.eterbase.exchange>`__                                                  | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `EXMO <https://exmo.me/?ref=131685>`__                                                  | 1.1 | `API <https://exmo.me/en/api_doc?ref=131685>`__                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `EXX <https://www.exx.com/r/fde4260159e53ab8a58cc9186d35501f?recommQd=1>`__             | \*  | `API <https://www.exx.com/help/restApi>`__                                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `FCoin <https://www.fcoin.com/i/Z5P7V>`__                                               | 2   | `API <https://developer.fcoin.com>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `FCoinJP <https://www.fcoinjp.com>`__                                                   | 2   | `API <https://developer.fcoin.com>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `flowBTC <https://www.flowbtc.com.br>`__                                                | 1   | `API <https://www.flowbtc.com.br/api.html>`__                                                   |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `FoxBit <https://foxbit.com.br/exchange>`__                                             | 1   | `API <https://foxbit.com.br/api/>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `FTX <https://ftx.com/#a=1623029>`__                                                    | \*  | `API <https://github.com/ftexchange/ftx>`__                                                     | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `FYB-SE <https://www.fybse.se>`__                                                       | \*  | `API <https://fyb.docs.apiary.io>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Gate.io <https://www.gate.io/signup/2436035>`__                                        | 2   | `API <https://gate.io/api2>`__                                                                  |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Gemini <https://gemini.com/>`__                                                        | 1   | `API <https://docs.gemini.com/rest-api>`__                                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `HBTC <https://www.hbtc.com/register/O2S8NS>`__                                         | 1   | `API <https://github.com/bhexopen/BHEX-OpenApi/tree/master/doc>`__                              |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `HitBTC <https://hitbtc.com/?ref_id=5a5d39a65d466>`__                                   | 2   | `API <https://api.hitbtc.com>`__                                                                |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `HollaEx <https://pro.hollaex.com/signup?affiliation_code=QSWA6G>`__                    | 1   | `API <https://apidocs.hollaex.com>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Huobi Pro <https://www.huobi.co/en-us/topic/invited/?invite_code=rwrd3>`__             | 1   | `API <https://huobiapi.github.io/docs/spot/v1/cn/>`__                                           |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Huobi Russia <https://www.huobi.com.ru/invite?invite_code=esc74>`__                    | 1   | `API <https://github.com/cloudapidoc/API_Docs_en>`__                                            |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ICE3X <https://ice3x.com?ref=14341802>`__                                              | 1   | `API <https://ice3x.co.za/ice-cubed-bitcoin-exchange-api-documentation-1-june-2017>`__          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `IDEX <https://idex.market>`__                                                          | \*  | `API <https://docs.idex.market/>`__                                                             | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Independent Reserve <https://www.independentreserve.com>`__                            | \*  | `API <https://www.independentreserve.com/API>`__                                                |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `INDODAX <https://indodax.com/ref/testbitcoincoid/1>`__                                 | 2.0 | `API <https://indodax.com/downloads/BITCOINCOID-API-DOCUMENTATION.pdf>`__                       |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `itBit <https://www.itbit.com>`__                                                       | 1   | `API <https://api.itbit.com/docs>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `KKEX <https://kkex.com>`__                                                             | 2   | `API <https://kkex.com/api_wiki/cn/>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Kraken <https://www.kraken.com>`__                                                     | 0   | `API <https://www.kraken.com/features/api>`__                                                   | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `KuCoin <https://www.kucoin.com/?rcode=E5wkqe>`__                                       | 2   | `API <https://docs.kucoin.com>`__                                                               |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Kuna <https://kuna.io?r=kunaid-gvfihe8az7o4>`__                                        | 2   | `API <https://kuna.io/documents/api>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `LakeBTC <https://www.lakebtc.com>`__                                                   | 2   | `API <https://www.lakebtc.com/s/api_v2>`__                                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Latoken <https://latoken.com>`__                                                       | 1   | `API <https://api.latoken.com>`__                                                               |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `LBank <https://www.lbex.io/invite?icode=7QCY>`__                                       | 1   | `API <https://github.com/LBank-exchange/lbank-official-api-docs>`__                             |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Liquid <https://www.liquid.com?affiliate=SbzC62lt30976>`__                             | 2   | `API <https://developers.liquid.com>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `LiveCoin <https://livecoin.net/?from=Livecoin-CQ1hfx44>`__                             | \*  | `API <https://www.livecoin.net/api?lang=en>`__                                                  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `luno <https://www.luno.com/invite/44893A>`__                                           | 1   | `API <https://www.luno.com/en/api>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Lykke <https://www.lykke.com>`__                                                       | 1   | `API <https://hft-api.lykke.com/swagger/ui/>`__                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Mercado Bitcoin <https://www.mercadobitcoin.com.br>`__                                 | 3   | `API <https://www.mercadobitcoin.com.br/api-doc>`__                                             |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `MixCoins <https://mixcoins.com>`__                                                     | 1   | `API <https://mixcoins.com/help/api/>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `OceanEx <https://oceanex.pro/signup?referral=VE24QX>`__                                | 1   | `API <https://api.oceanex.pro/doc/v1>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `OKCoin <https://www.okcoin.com/account/register?flag=activity&channelId=*********>`__  | 3   | `API <https://www.okcoin.com/docs/en/>`__                                                       |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `OKEX <https://www.okex.com/join/1888677>`__                                            | 3   | `API <https://www.okex.com/docs/en/>`__                                                         |                                                                      | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Paymium <https://www.paymium.com/page/sign-up?referral=eDAzPoRQFMvaAB8sf-qj>`__        | 1   | `API <https://github.com/Paymium/api-documentation>`__                                          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Poloniex <https://poloniex.com/signup?c=UBFZJRPJ>`__                                   | \*  | `API <https://docs.poloniex.com>`__                                                             | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ProBit <https://www.probit.com/r/34608773>`__                                          | 1   | `API <https://docs-en.probit.com>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `qTrade <https://qtrade.io/?ref=BKOQWVFGRH2C>`__                                        | 1   | `API <https://qtrade-exchange.github.io/qtrade-docs>`__                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `RightBTC <https://www.rightbtc.com>`__                                                 | \*  | `API <https://docs.rightbtc.com/api/>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `SouthXchange <https://www.southxchange.com>`__                                         | \*  | `API <https://www.southxchange.com/Home/Api>`__                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `STEX <https://app.stex.com?ref=36416021>`__                                            | 3   | `API <https://help.stex.com/en/collections/1593608-api-v3-documentation>`__                     |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Stronghold <https://stronghold.co>`__                                                  | 1   | `API <https://docs.stronghold.co>`__                                                            |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `SurBitcoin <https://surbitcoin.com>`__                                                 | 1   | `API <https://blinktrade.com/docs>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `The Ocean <https://theocean.trade>`__                                                  | 1   | `API <https://docs.theocean.trade>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `TheRockTrading <https://therocktrading.com>`__                                         | 1   | `API <https://api.therocktrading.com/doc/v1/index.html>`__                                      |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `TideBit <http://bit.ly/2IX0LrM>`__                                                     | 2   | `API <https://www.tidebit.com/documents/api/guide>`__                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Tidex <https://tidex.com/exchange/?ref=57f5638d9cd7>`__                                | 3   | `API <https://tidex.com/exchange/public-api>`__                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `TimeX <https://timex.io/?refcode=1x27vNkTbP1uwkCck>`__                                 | 1   | `API <https://docs.timex.io>`__                                                                 |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `TOP.Q <https://www.bw.com/regGetCommission/N3JuT1R3bWxKTE0>`__                         | 1   | `API <https://github.com/topq-exchange/api_docs_en/wiki/REST_api_reference>`__                  |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Upbit <https://upbit.com>`__                                                           | 1   | `API <https://docs.upbit.com/docs/%EC%9A%94%EC%B2%AD-%EC%88%98-%EC%A0%9C%ED%95%9C>`__           | `CCXT Certified <https://github.com/ccxt/ccxt/wiki/Certification>`__ | `CCXT Pro <https://ccxt.pro>`__ |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Vaultoro <https://www.vaultoro.com>`__                                                 | 1   | `API <https://api.vaultoro.com>`__                                                              |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `VBTC <https://vbtc.exchange>`__                                                        | 1   | `API <https://blinktrade.com/docs>`__                                                           |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `WhiteBit <https://whitebit.com/referral/d9bdf40e-28f2-4b52-b2f9-cd1415d82963>`__       | 2   | `API <https://documenter.getpostman.com/view/7473075/SVSPomwS?version=latest#intro>`__          |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `xBTCe <https://xbtce.com/?agent=XX97BTCXXXG687021000B>`__                              | 1   | `API <https://www.xbtce.com/tradeapi>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `YoBit <https://www.yobit.net>`__                                                       | 3   | `API <https://www.yobit.net/en/api/>`__                                                         |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `Zaif <https://zaif.jp>`__                                                              | 1   | `API <https://techbureau-api-document.readthedocs.io/ja/latest/index.html>`__                   |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+
| `ZB <https://www.zb.com>`__                                                             | 1   | `API <https://www.zb.com/i/developer>`__                                                        |                                                                      |                                 |
+-----------------------------------------------------------------------------------------+-----+-------------------------------------------------------------------------------------------------+----------------------------------------------------------------------+---------------------------------+

The list above is updated frequently, new crypto markets, exchanges, bug fixes, and API endpoints are introduced on a regular basis. See the `Manual <https://github.com/ccxt/ccxt/wiki>`__ for more details. If you can’t find a cryptocurrency exchange in the list above and want it to be added, post a link to it by opening an issue here on GitHub or send us an email.

The library is under `MIT license <https://github.com/ccxt/ccxt/blob/master/LICENSE.txt>`__, that means it’s absolutely free for any developer to build commercial and opensource software on top of it, but use it at your own risk with no warranties, as is.

--------------

Install
-------

The easiest way to install the CCXT library is to use a package manager:

-  `ccxt in NPM <https://www.npmjs.com/package/ccxt>`__ (JavaScript / Node v7.6+)
-  `ccxt in PyPI <https://pypi.python.org/pypi/ccxt>`__ (Python 3.5.3+)
-  `ccxt in Packagist/Composer <https://packagist.org/packages/ccxt/ccxt>`__ (PHP 5.4+)

This library is shipped as an all-in-one module implementation with minimalistic dependencies and requirements:

-  ```js/`` <https://github.com/ccxt/ccxt/blob/master/js/>`__ in JavaScript
-  ```python/`` <https://github.com/ccxt/ccxt/blob/master/python/>`__ in Python (generated from JS)
-  ```php/`` <https://github.com/ccxt/ccxt/blob/master/php/>`__ in PHP (generated from JS)

You can also clone it into your project directory from `ccxt GitHub repository <https://github.com/ccxt/ccxt>`__:

.. code:: shell

   git clone https://github.com/ccxt/ccxt.git

JavaScript (NPM)
~~~~~~~~~~~~~~~~

JavaScript version of CCXT works in both Node and web browsers. Requires ES6 and ``async/await`` syntax support (Node 7.6.0+). When compiling with Webpack and Babel, make sure it is `not excluded <https://github.com/ccxt/ccxt/issues/225#issuecomment-*********>`__ in your ``babel-loader`` config.

`ccxt in NPM <https://www.npmjs.com/package/ccxt>`__

.. code:: shell

   npm install ccxt

.. code:: javascript

   var ccxt = require ('ccxt')

   console.log (ccxt.exchanges) // print all available exchanges

JavaScript (for use with the ``<script>`` tag):
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

All-in-one browser bundle (dependencies included), served from a CDN of your choice:

-  jsDelivr: https://cdn.jsdelivr.net/npm/ccxt@1.29.74/dist/ccxt.browser.js
-  unpkg: https://unpkg.com/ccxt@1.29.74/dist/ccxt.browser.js

CDNs are not updated in real-time and may have delays. Defaulting to the most recent version without specifying the version number is not recommended. Please, keep in mind that we are not responsible for the correct operation of those CDN servers.

.. code:: html

   <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/ccxt@1.29.74/dist/ccxt.browser.js"></script>

Creates a global ``ccxt`` object:

.. code:: javascript

   console.log (ccxt.exchanges) // print all available exchanges

Python
~~~~~~

`ccxt in PyPI <https://pypi.python.org/pypi/ccxt>`__

.. code:: shell

   pip install ccxt

.. code:: python

   import ccxt
   print(ccxt.exchanges) # print a list of all available exchange classes

The library supports concurrent asynchronous mode with asyncio and async/await in Python 3.5.3+

.. code:: python

   import ccxt.async_support as ccxt # link against the asynchronous version of ccxt

PHP
~~~

`ccxt in PHP with Packagist/Composer <https://packagist.org/packages/ccxt/ccxt>`__ (PHP 5.4+)

It requires common PHP modules:

-  cURL
-  mbstring (using UTF-8 is highly recommended)
-  PCRE
-  iconv
-  gmp (this is a built-in extension as of PHP 7.2+)

.. code:: php

   include "ccxt.php";
   var_dump (\ccxt\Exchange::$exchanges); // print a list of all available exchange classes

Docker
~~~~~~

You can get CCXT installed in a container along with all the supported languages and dependencies. This may be useful if you want to contribute to CCXT (e.g. run the build scripts and tests — please see the `Contributing <https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md>`__ document for the details on that).

Using ``docker-compose`` (in the cloned CCXT repository):

.. code:: shell

   docker-compose run --rm ccxt

You don’t need the Docker image if you’re not going to develop CCXT. If you just want to use CCXT – just install it as a regular package into your project.

--------------

Documentation
-------------

Read the `Manual <https://github.com/ccxt/ccxt/wiki>`__ for more details.

Usage
-----

Intro
~~~~~

The CCXT library consists of a public part and a private part. Anyone can use the public part immediately after installation. Public APIs provide unrestricted access to public information for all exchange markets without the need to register a user account or have an API key.

Public APIs include the following:

-  market data
-  instruments/trading pairs
-  price feeds (exchange rates)
-  order books
-  trade history
-  tickers
-  OHLC(V) for charting
-  other public endpoints

In order to trade with private APIs you need to obtain API keys from an exchange’s website. It usually means signing up to the exchange and creating API keys for your account. Some exchanges require personal info or identification. Sometimes verification may be necessary as well. In this case you will need to register yourself, this library will not create accounts or API keys for you. Some exchanges expose API endpoints for registering an account, but most exchanges don’t. You will have to sign up and create API keys on their websites.

Private APIs allow the following:

-  manage personal account info
-  query account balances
-  trade by making market and limit orders
-  deposit and withdraw fiat and crypto funds
-  query personal orders
-  get ledger history
-  transfer funds between accounts
-  use merchant services

This library implements full public and private REST APIs for all exchanges. WebSocket and FIX implementations in JavaScript, PHP, Python are available in `CCXT Pro <https://ccxt.pro>`__, which is a professional addon to CCXT with support for WebSocket streams.

The CCXT library supports both camelcase notation (preferred in JavaScript) and underscore notation (preferred in Python and PHP), therefore all methods can be called in either notation or coding style in any language.

.. code:: javascript

   // both of these notations work in JavaScript/Python/PHP
   exchange.methodName ()  // camelcase pseudocode
   exchange.method_name () // underscore pseudocode

Read the `Manual <https://github.com/ccxt/ccxt/wiki>`__ for more details.

JavaScript
~~~~~~~~~~

.. code:: javascript

   'use strict';
   const ccxt = require ('ccxt');

   (async function () {
       let kraken    = new ccxt.kraken ()
       let bitfinex  = new ccxt.bitfinex ({ verbose: true })
       let huobipro  = new ccxt.huobipro ()
       let okcoinusd = new ccxt.okcoinusd ({
           apiKey: 'YOUR_PUBLIC_API_KEY',
           secret: 'YOUR_SECRET_PRIVATE_KEY',
       })

       const exchangeId = 'binance'
           , exchangeClass = ccxt[exchangeId]
           , exchange = new exchangeClass ({
               'apiKey': 'YOUR_API_KEY',
               'secret': 'YOUR_SECRET',
               'timeout': 30000,
               'enableRateLimit': true,
           })

       console.log (kraken.id,    await kraken.loadMarkets ())
       console.log (bitfinex.id,  await bitfinex.loadMarkets  ())
       console.log (huobipro.id,  await huobipro.loadMarkets ())

       console.log (kraken.id,    await kraken.fetchOrderBook (kraken.symbols[0]))
       console.log (bitfinex.id,  await bitfinex.fetchTicker ('BTC/USD'))
       console.log (huobipro.id,  await huobipro.fetchTrades ('ETH/CNY'))

       console.log (okcoinusd.id, await okcoinusd.fetchBalance ())

       // sell 1 BTC/USD for market price, sell a bitcoin for dollars immediately
       console.log (okcoinusd.id, await okcoinusd.createMarketSellOrder ('BTC/USD', 1))

       // buy 1 BTC/USD for $2500, you pay $2500 and receive ฿1 when the order is closed
       console.log (okcoinusd.id, await okcoinusd.createLimitBuyOrder ('BTC/USD', 1, 2500.00))

       // pass/redefine custom exchange-specific order params: type, amount, price or whatever
       // use a custom order type
       bitfinex.createLimitSellOrder ('BTC/USD', 1, 10, { 'type': 'trailing-stop' })

   }) ();

.. _python-1:

Python
~~~~~~

.. code:: python

   # coding=utf-8

   import ccxt

   hitbtc   = ccxt.hitbtc({'verbose': True})
   bitmex   = ccxt.bitmex()
   huobipro = ccxt.huobipro()
   exmo     = ccxt.exmo({
       'apiKey': 'YOUR_PUBLIC_API_KEY',
       'secret': 'YOUR_SECRET_PRIVATE_KEY',
   })
   kraken = ccxt.kraken({
       'apiKey': 'YOUR_PUBLIC_API_KEY',
       'secret': 'YOUR_SECRET_PRIVATE_KEY',
   })

   exchange_id = 'binance'
   exchange_class = getattr(ccxt, exchange_id)
   exchange = exchange_class({
       'apiKey': 'YOUR_API_KEY',
       'secret': 'YOUR_SECRET',
       'timeout': 30000,
       'enableRateLimit': True,
   })

   hitbtc_markets = hitbtc.load_markets()

   print(hitbtc.id, hitbtc_markets)
   print(bitmex.id, bitmex.load_markets())
   print(huobipro.id, huobipro.load_markets())

   print(hitbtc.fetch_order_book(hitbtc.symbols[0]))
   print(bitmex.fetch_ticker('BTC/USD'))
   print(huobipro.fetch_trades('LTC/CNY'))

   print(exmo.fetch_balance())

   # sell one ฿ for market price and receive $ right now
   print(exmo.id, exmo.create_market_sell_order('BTC/USD', 1))

   # limit buy BTC/EUR, you pay €2500 and receive ฿1  when the order is closed
   print(exmo.id, exmo.create_limit_buy_order('BTC/EUR', 1, 2500.00))

   # pass/redefine custom exchange-specific order params: type, amount, price, flags, etc...
   kraken.create_market_buy_order('BTC/USD', 1, {'trading_agreement': 'agree'})

.. _php-1:

PHP
~~~

.. code:: php

   include 'ccxt.php';

   $poloniex = new \ccxt\poloniex ();
   $bittrex  = new \ccxt\bittrex  (array ('verbose' => true));
   $quoinex  = new \ccxt\quoinex   ();
   $zaif     = new \ccxt\zaif     (array (
       'apiKey' => 'YOUR_PUBLIC_API_KEY',
       'secret' => 'YOUR_SECRET_PRIVATE_KEY',
   ));
   $hitbtc   = new \ccxt\hitbtc   (array (
       'apiKey' => 'YOUR_PUBLIC_API_KEY',
       'secret' => 'YOUR_SECRET_PRIVATE_KEY',
   ));

   $exchange_id = 'binance';
   $exchange_class = "\\ccxt\\$exchange_id";
   $exchange = new $exchange_class (array (
       'apiKey' => 'YOUR_API_KEY',
       'secret' => 'YOUR_SECRET',
       'timeout' => 30000,
       'enableRateLimit' => true,
   ));

   $poloniex_markets = $poloniex->load_markets ();

   var_dump ($poloniex_markets);
   var_dump ($bittrex->load_markets ());
   var_dump ($quoinex->load_markets ());

   var_dump ($poloniex->fetch_order_book ($poloniex->symbols[0]));
   var_dump ($bittrex->fetch_trades ('BTC/USD'));
   var_dump ($quoinex->fetch_ticker ('ETH/EUR'));
   var_dump ($zaif->fetch_ticker ('BTC/JPY'));

   var_dump ($zaif->fetch_balance ());

   // sell 1 BTC/JPY for market price, you pay ¥ and receive ฿ immediately
   var_dump ($zaif->id, $zaif->create_market_sell_order ('BTC/JPY', 1));

   // buy BTC/JPY, you receive ฿1 for ¥285000 when the order closes
   var_dump ($zaif->id, $zaif->create_limit_buy_order ('BTC/JPY', 1, 285000));

   // set a custom user-defined id to your order
   $hitbtc->create_order ('BTC/USD', 'limit', 'buy', 1, 3000, array ('clientOrderId' => '123'));

Contributing
------------

Please read the `CONTRIBUTING <https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md>`__ document before making changes that you would like adopted in the code. Also, read the `Manual <https://github.com/ccxt/ccxt/wiki>`__ for more details.

Support Developer Team
----------------------

We are investing a significant amount of time into the development of this library. If CCXT made your life easier and you want to help us improve it further, or if you want to speed up development of new features and exchanges, please support us with a tip. We appreciate all contributions!

Sponsors
~~~~~~~~

Support this project by becoming a sponsor. Your logo will show up here with a link to your website.

[`Become a sponsor <https://opencollective.com/ccxt#sponsor>`__]

Supporters
~~~~~~~~~~

Support this project by becoming a supporter. Your avatar will show up here with a link to your website.

[`Become a supporter <https://opencollective.com/ccxt#supporter>`__]

Backers
~~~~~~~

Thank you to all our backers! [`Become a backer <https://opencollective.com/ccxt#backer>`__]

Crypto
~~~~~~

::

   ETH ****************************************** (ETH only)
   BTC **********************************
   BCH **********************************
   LTC LbT8mkAqQBphc4yxLXEDgYDfEax74et3bP

Thank you!

Social
------

-  `Follow us on Twitter <https://twitter.com/ccxt_official>`__
-  `Read our blog on Medium <https://medium.com/@ccxt>`__
-  \ `Discord <https://discord.gg/dhzSKYU>`__\ 

Team
----

-  `Igor Kroitor <https://github.com/kroitor>`__
-  `Carlo Revelli <https://github.com/frosty00>`__

Contact Us
----------

For business inquiries: <EMAIL>
