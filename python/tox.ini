[tox]
envlist = py{37,38,39,310,311}
skipsdist = True
skip_missing_interpreters = False
ignore_basepython_conflict = True

[testenv]
basepython = python3
sitepackages = True
setenv =
    PYTHONPATH = {toxinidir}:{toxinidir}
deps =
    pip
    setuptools
    wheel
    aiohttp
    cryptography
    requests
commands =
    pip install -e .
    python ccxt/test/tests_init.py --idTests
    python ccxt/test/tests_init.py --sync --idTests

[testenv:qa]
allowlist_externals = ruff
changedir = {toxinidir}
commands = ruff ./ccxt/
deps = .[qa]

[testenv:type]
allowlist_externals = mypy
changedir = {toxinidir}
commands = mypy ./ccxt/
deps = .[type]
