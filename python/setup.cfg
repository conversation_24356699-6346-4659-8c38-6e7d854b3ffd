[bdist_wheel]
# This flag says that the code is written to work on both Python 2 and Python
# 3. If at all possible, it is good practice to do this. If you cannot, you
# will need to generate wheels for each Python version that you support.
universal=1

[flake8]
ignore = E501
exclude =
    .ropeproject,
    .tox,
    .eggs,
    # No need to traverse our git directory
    .git,
    # There's no value in checking cache directories
    __pycache__,
    # Other special cases
    node_modules,
    .nyc_output,
    build,
    tmp,
    # No need to check the basecode
    ccxt.py
