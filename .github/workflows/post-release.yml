name: On release published (pushback changelog)

on:
  workflow_run:
    workflows: ["Release workflow"]
    types:
      - completed
jobs:
  changelog:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    name: Update changelog
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_TOKEN }}
          fetch-depth: 2
      - uses: rhysd/changelog-from-release/action@v3
        with:
          file: CHANGELOG.md
          github_token: ${{ secrets.GH_TOKEN }}
  failed-release:
    if: ${{ github.event.workflow_run.conclusion != 'success' }}
    runs-on: ubuntu-latest
    steps:
      - name: Skip changelog update
        run: echo "Won't continue because the Release workflow failed."