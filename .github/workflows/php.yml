name: PHP

on:
  workflow_dispatch:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    if: ${{ !startsWith(github.event.head_commit.message, 'Update changelog for') && !startsWith(github.event.head_commit.message, '[Automated changes]') && !contains(github.event.head_commit.message, 'Merge branch ''master'' of https://github.com/ccxt/ccxt') }}
    steps:
    - name: Print vars
      run: |
          echo "GITHUB_ACTOR: {{ github.actor }}"
          echo "GITHUB_EVENT_NAME: {{ github.event_name }}"
          echo "GITHUB_EVENT_PUSH_NAME: {{ github.event.pusher.name }}"
          echo "GITHUB_REF: {{ github.ref }}"
    - uses: actions/checkout@v4
      if: github.ref == 'refs/heads/master'
      with:
        token: ${{ secrets.GH_TOKEN }}
        fetch-depth: 2
    - uses: actions/checkout@v4
      if: github.ref != 'refs/heads/master'
      with:
        fetch-depth: 2
    - name: Skip Automated Push Commits
      id: check_commit
      run: |
        if [[ "${{ github.event.head_commit.message }}" == *"[Automated changes]"* ]]; then
          echo "This commit was made by the action. Skipping."
          exit 0
        fi
    - name: Setup PHP with PECL extension
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.4.6'
        # cache: true
        ini-values: |
          zend.assertions=1
          display_errors=On
    - uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    - name: Install npm dependencies
      run: npm ci --include=dev
    - name: Composer install
      run: composer install
    - name: Determine modified files
      run: ./utils/init_actions.sh
    - name: Pre-transpile (export exchanges and emit API)
      run: npm run pre-transpile-php
    - name: Transpile to PHP
      if: env.important_modified == 'true'
      run: npm run force-transpile-fast-php
    - name: Transpile to PHP (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run transpileRest -- --php $exchange
        done
        cleaned_ws_files=$(echo ${{ env.ws_files }} | tr -s ' ')
        for exchange in $cleaned_ws_files; do
          npm run transpileWs -- --php $exchange
        done
    - name: Post-transpile (syntax check)
      run: npm run check-php-syntax
    - name: Run Base Tests
      if: env.important_modified == 'true'
      run: npm run test-base-rest-php
    - name: Run Base Ws Tests
      if: env.important_modified == 'true'
      run: npm run test-base-ws-php
    - name: Run Id Tests
      if: env.important_modified == 'true'
      run: npm run id-tests-php
    - name: Request tests
      if: env.important_modified == 'true'
      run: npm run request-php
    - name: Request tests (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run request-php-sync -- $exchange
          npm run request-php-async -- $exchange
        done
    - name: Response tests
      if: env.important_modified == 'true'
      run: npm run response-php
    - name: Response tests (specific)
      if: env.important_modified == 'false'
      run: |
        cleaned_rest_files=$(echo ${{ env.rest_files }} | tr -s ' ')
        for exchange in $cleaned_rest_files; do
          npm run response-php-sync -- $exchange
          npm run response-php-async -- $exchange
        done
    - name: Upload shared_env.txt
      uses: actions/upload-artifact@v4
      with:
          name: shared_env
          path: shared_env.txt

    - name: Upload PHP Files
      uses: actions/upload-artifact@v4
      with:
        name: php-files
        path: php/

    - name: Push PHP changes to master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      if: github.ref == 'refs/heads/master'
      run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git add php/
          git commit -m "[Automated changes] PHP files" || echo "No changes to commit."
          git remote set-url origin https://${GITHUB_TOKEN}@github.com/${{ github.repository }}
          git config --global pull.rebase false
          git pull --rebase --autostash origin master
          git push

  live-tests:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Download shared_env
        uses: actions/download-artifact@v4
        with:
          name: shared_env
      - name: Download PHP Files
        uses: actions/download-artifact@v4
        with:
          name: php-files
          path: php/
      - name: Restore shared_env
        run: ./utils/restore_shared_env.sh
      - name: Setup PHP with PECL extension
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4.6'
          extensions: protobuf
          # cache: true
          ini-values: |
            zend.assertions=1
            display_errors=On
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      - name: Install npm dependencies
        run: npm ci --include=dev
      - name: Composer install
        run: composer install
      - name: Export exchanges
        run: npm run export-exchanges
      - name: Live tests
        if: env.important_modified == 'true'
        run: ./run-tests-simul.sh --php-async
      - name: Live tests (specific)
        if: env.important_modified == 'false'
        run: ./run-tests-simul.sh --php-async "${{ env.rest_files }}" "${{ env.ws_files }}"